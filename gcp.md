PROJECT_ID=powerful-surf-469320-f1
REGION=us-west2


gcloud init
gcloud services enable artifactregistry.googleapis.com

gcloud auth configure-docker \
    us-west2-docker.pkg.dev

docker buildx build --platform linux/amd64 -t us-west2-docker.pkg.dev/powerful-surf-469320-f1/nuvineerapp/nuvineer-app:latest . --push
# docker push us-west2-docker.pkg.dev/powerful-surf-469320-f1/nuvineerapp/nuvineer-app:latest
OR
gcloud builds submit --tag us-west2-docker.pkg.dev/powerful-surf-469320-f1/nuvineerapp/nuvineer-app:latest .


gcloud run deploy nuvineer-app-service \
    --image us-west2-docker.pkg.dev/powerful-surf-469320-f1/nuvineerapp/nuvineer-app:latest \
    --platform managed \
    --region us-west2 \
    --allow-unauthenticated

# Build & Deploy Nuvineer Worker DEV on GCP

docker buildx build --platform linux/amd64 -t us-west2-docker.pkg.dev/powerful-surf-469320-f1/nuvineerapp/nuvineer-worker-dev:latest . --push
    gcloud run deploy nuvineer-worker-dev-service \
        --image us-west2-docker.pkg.dev/powerful-surf-469320-f1/nuvineerapp/nuvineer-worker-dev:latest \
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        --platform managed \
        --region us-west2 \
        --allow-unauthenticated

# Build & Deploy Nuvineer Worker on GCP

docker buildx build --platform linux/amd64 -t us-west2-docker.pkg.dev/powerful-surf-469320-f1/nuvineerapp/nuvineer-worker:latest . --push

gcloud run deploy nuvineer-worker-service \
    --image us-west2-docker.pkg.dev/powerful-surf-469320-f1/nuvineerapp/nuvineer-worker:latest \
    --set-env-vars ENV=production,DEBUG=false,CRON_SECRET="N7KOmJvgTp8GHNhX4BOhOup1O56j7eEj1t+xKS2PAAw=",SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnYXpnZ3VqcGVkZHRrb3BhcWNvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU0NjI4ODcsImV4cCI6MjA2MTAzODg4N30.9FHkcE1PVQn6pdarXfLh4uvjUigqLUdU2G6cwQZjrlQ",SUPABASE_URL="https://cgazggujpeddtkopaqco.supabase.co",SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnYXpnZ3VqcGVkZHRrb3BhcWNvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTQ2Mjg4NywiZXhwIjoyMDYxMDM4ODg3fQ.Km1rm9wiM-u4braaV8qR8pqFWBX5EXafaj8srwTCTys",ANTHROPIC_API_KEY="************************************************************************************************************",GITHUB_APP_ID="1228193",GITHUB_APP_PRIVATE_KEY="**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",GITHUB_TOKEN="*********************************************************************************************",OPENAI_API_KEY="********************************************************************************************************************************************************************" \
    --platform managed \
    --region us-west2 \
    --allow-unauthenticated
