{"version": 3, "file": "ts-compiler-types.js", "sourceRoot": "", "sources": ["../src/ts-compiler-types.ts"], "names": [], "mappings": "", "sourcesContent": ["import type * as _ts from 'typescript';\n\n/**\n * Common TypeScript interfaces between versions.  We endeavour to write ts-node's own code against these types instead\n * of against `import \"typescript\"`, though we are not yet doing this consistently.\n *\n * Sometimes typescript@next adds an API we need to use.  But we build ts-node against typescript@latest.\n * In these cases, we must declare that API explicitly here.  Our declarations include the newer typescript@next APIs.\n * Importantly, these re-declarations are *not* TypeScript internals.  They are public APIs that only exist in\n * pre-release versions of typescript.\n */\nexport interface TSCommon {\n  version: typeof _ts.version;\n  sys: typeof _ts.sys;\n  ScriptSnapshot: typeof _ts.ScriptSnapshot;\n  displayPartsToString: typeof _ts.displayPartsToString;\n  createLanguageService: typeof _ts.createLanguageService;\n  getDefaultLibFilePath: typeof _ts.getDefaultLibFilePath;\n  getPreEmitDiagnostics: typeof _ts.getPreEmitDiagnostics;\n  flattenDiagnosticMessageText: typeof _ts.flattenDiagnosticMessageText;\n  transpileModule: typeof _ts.transpileModule;\n  ModuleKind: TSCommon.ModuleKindEnum;\n  ScriptTarget: typeof _ts.ScriptTarget;\n  findConfigFile: typeof _ts.findConfigFile;\n  readConfigFile: typeof _ts.readConfigFile;\n  parseJsonConfigFileContent: typeof _ts.parseJsonConfigFileContent;\n  formatDiagnostics: typeof _ts.formatDiagnostics;\n  formatDiagnosticsWithColorAndContext: typeof _ts.formatDiagnosticsWithColorAndContext;\n\n  createDocumentRegistry: typeof _ts.createDocumentRegistry;\n  JsxEmit: typeof _ts.JsxEmit;\n  createModuleResolutionCache: typeof _ts.createModuleResolutionCache;\n  resolveModuleName: typeof _ts.resolveModuleName;\n  resolveModuleNameFromCache: typeof _ts.resolveModuleNameFromCache;\n  resolveTypeReferenceDirective: typeof _ts.resolveTypeReferenceDirective;\n  createIncrementalCompilerHost: typeof _ts.createIncrementalCompilerHost;\n  createSourceFile: typeof _ts.createSourceFile;\n  getDefaultLibFileName: typeof _ts.getDefaultLibFileName;\n  createIncrementalProgram: typeof _ts.createIncrementalProgram;\n  createEmitAndSemanticDiagnosticsBuilderProgram: typeof _ts.createEmitAndSemanticDiagnosticsBuilderProgram;\n\n  Extension: typeof _ts.Extension;\n  ModuleResolutionKind: typeof _ts.ModuleResolutionKind;\n}\nexport namespace TSCommon {\n  export interface LanguageServiceHost extends _ts.LanguageServiceHost {}\n  export type ModuleResolutionHost = _ts.ModuleResolutionHost;\n  export type ParsedCommandLine = _ts.ParsedCommandLine;\n  export type ResolvedModule = _ts.ResolvedModule;\n  export type ResolvedTypeReferenceDirective =\n    _ts.ResolvedTypeReferenceDirective;\n  export type CompilerOptions = _ts.CompilerOptions;\n  export type ResolvedProjectReference = _ts.ResolvedProjectReference;\n  export type ResolvedModuleWithFailedLookupLocations =\n    _ts.ResolvedModuleWithFailedLookupLocations;\n  export type FileReference = _ts.FileReference;\n  export type SourceFile = _ts.SourceFile;\n  // Hack until we start building against TS >= 4.7.0\n  export type ModuleKindEnum = typeof _ts.ModuleKind & {\n    Node16: typeof _ts.ModuleKind extends { Node16: any }\n      ? typeof _ts.ModuleKind['Node16']\n      : 100;\n  };\n  // Can't figure out how to re-export an enum\n  // `export import ... =` complains that _ts is type-only import\n  export namespace ModuleKind {\n    export type CommonJS = _ts.ModuleKind.CommonJS;\n    export type ESNext = _ts.ModuleKind.ESNext;\n  }\n}\n\n/**\n * Compiler APIs we use that are marked internal and not included in TypeScript's public API declarations\n * @internal\n */\nexport interface TSInternal {\n  // https://github.com/microsoft/TypeScript/blob/4a34294908bed6701dcba2456ca7ac5eafe0ddff/src/compiler/core.ts#L1906-L1909\n  createGetCanonicalFileName(\n    useCaseSensitiveFileNames: boolean\n  ): TSInternal.GetCanonicalFileName;\n  // https://github.com/microsoft/TypeScript/blob/c117c266e09c80e8a06b24a6e94b9d018f5fae6b/src/compiler/commandLineParser.ts#L2054\n  convertToTSConfig(\n    configParseResult: _ts.ParsedCommandLine,\n    configFileName: string,\n    host: TSInternal.ConvertToTSConfigHost\n  ): any;\n  libs?: string[];\n  Diagnostics: {\n    File_0_not_found: _ts.DiagnosticMessage;\n  };\n  createCompilerDiagnostic(\n    message: _ts.DiagnosticMessage,\n    ...args: (string | number | undefined)[]\n  ): _ts.Diagnostic;\n  nodeModuleNameResolver(\n    moduleName: string,\n    containingFile: string,\n    compilerOptions: _ts.CompilerOptions,\n    host: _ts.ModuleResolutionHost,\n    cache?: _ts.ModuleResolutionCache,\n    redirectedReference?: _ts.ResolvedProjectReference,\n    conditionsOrIsConfigLookup?: string[] | boolean, // `conditions` parameter added in TS 5.3\n    isConfigLookup?: boolean\n  ): _ts.ResolvedModuleWithFailedLookupLocations;\n  // Added in TS 4.7\n  getModeForFileReference?: (\n    ref: _ts.FileReference | string,\n    containingFileMode: _ts.SourceFile['impliedNodeFormat']\n  ) => _ts.SourceFile['impliedNodeFormat'];\n  // TODO do we need these?  Which TS version adds them?\n  getPatternFromSpec(\n    spec: string,\n    basePath: string,\n    usage: 'files' | 'directories' | 'exclude'\n  ): string | undefined;\n  getRegularExpressionForWildcard(\n    specs: readonly string[] | undefined,\n    basePath: string,\n    usage: 'files' | 'directories' | 'exclude'\n  ): string | undefined;\n  // Added in TS 4.7\n  getModeForResolutionAtIndex?(\n    file: TSInternal.SourceFileImportsList,\n    index: number\n  ): _ts.SourceFile['impliedNodeFormat'];\n}\n/** @internal */\nexport namespace TSInternal {\n  // https://github.com/microsoft/TypeScript/blob/4a34294908bed6701dcba2456ca7ac5eafe0ddff/src/compiler/core.ts#L1906\n  export type GetCanonicalFileName = (fileName: string) => string;\n  // https://github.com/microsoft/TypeScript/blob/c117c266e09c80e8a06b24a6e94b9d018f5fae6b/src/compiler/commandLineParser.ts#L2041\n  export interface ConvertToTSConfigHost {\n    getCurrentDirectory(): string;\n    useCaseSensitiveFileNames: boolean;\n  }\n  // Note: is only a partial declaration, TS sources declare other fields\n  export interface SourceFileImportsList {\n    impliedNodeFormat?: TSCommon.SourceFile['impliedNodeFormat'];\n  }\n}\n"]}