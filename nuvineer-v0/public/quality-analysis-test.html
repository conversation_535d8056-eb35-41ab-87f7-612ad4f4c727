<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Decision Quality Analysis - Test Page</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto px-4 max-w-4xl">
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Decision Quality Analysis</h1>
            <p class="text-gray-600 mb-6">Test the quality analysis system with your repository data</p>
            
            <!-- Input Form -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
                <h2 class="text-xl font-semibold mb-4">Repository Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Repository Slug</label>
                        <input 
                            type="text" 
                            id="repositorySlug" 
                            placeholder="e.g., owner/repo-name"
                            value="mfts/papermark"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Installation ID</label>
                        <input 
                            type="number" 
                            id="installationId" 
                            placeholder="e.g., 12345"
                            value="0"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Lookback Days</label>
                        <input 
                            type="number" 
                            id="lookbackDays" 
                            value="90"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Min Decisions per Author</label>
                        <input 
                            type="number" 
                            id="minDecisions" 
                            value="3"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>
                </div>
                <div class="flex space-x-4">
                    <button 
                        onclick="runQuickCheck()" 
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                        Quick Check (GET)
                    </button>
                    <button 
                        onclick="runFullAnalysis()" 
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        Full Analysis (POST)
                    </button>
                </div>
            </div>

            <!-- Loading State -->
            <div id="loading" class="hidden text-center py-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <p class="mt-2 text-gray-600">Analyzing decision quality patterns...</p>
            </div>

            <!-- Results -->
            <div id="results" class="hidden">
                <h2 class="text-xl font-semibold mb-4">Analysis Results</h2>
                <pre id="resultsContent" class="bg-gray-900 text-green-400 p-4 rounded-lg overflow-auto text-sm whitespace-pre-wrap"></pre>
            </div>

            <!-- Error -->
            <div id="error" class="hidden bg-red-50 border border-red-200 rounded-lg p-4">
                <h3 class="text-red-800 font-semibold">Error</h3>
                <p id="errorMessage" class="text-red-600 mt-1"></p>
            </div>
        </div>

        <!-- API Documentation -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold mb-4">API Endpoints</h2>
            <div class="space-y-4">
                <div class="border-l-4 border-green-500 pl-4">
                    <h3 class="font-semibold text-green-700">GET /api/tech-debt/quality-analysis</h3>
                    <p class="text-gray-600 text-sm">Quick quality check - returns summary statistics</p>
                    <code class="text-xs bg-gray-100 p-1 rounded">?repository_slug=owner/repo&installation_id=123</code>
                </div>
                <div class="border-l-4 border-blue-500 pl-4">
                    <h3 class="font-semibold text-blue-700">POST /api/tech-debt/quality-analysis</h3>
                    <p class="text-gray-600 text-sm">Full analysis - returns detailed insights and recommendations</p>
                    <code class="text-xs bg-gray-100 p-1 rounded">Body: {"repository_slug": "owner/repo", "installation_id": 123, ...}</code>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showLoading() {
            document.getElementById('loading').classList.remove('hidden');
            document.getElementById('results').classList.add('hidden');
            document.getElementById('error').classList.add('hidden');
        }

        function showResults(data) {
            document.getElementById('loading').classList.add('hidden');
            document.getElementById('results').classList.remove('hidden');
            document.getElementById('resultsContent').textContent = JSON.stringify(data, null, 2);
        }

        function showError(message) {
            document.getElementById('loading').classList.add('hidden');
            document.getElementById('error').classList.remove('hidden');
            document.getElementById('errorMessage').textContent = message;
        }

        async function runQuickCheck() {
            const repositorySlug = document.getElementById('repositorySlug').value;
            const installationId = document.getElementById('installationId').value;

            if (!repositorySlug || !installationId) {
                showError('Please fill in Repository Slug and Installation ID');
                return;
            }

            showLoading();

            try {
                const url = `/api/tech-debt/quality-analysis?repository_slug=${encodeURIComponent(repositorySlug)}&installation_id=${installationId}`;
                const response = await fetch(url);
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.details || `HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                showResults(data);
            } catch (error) {
                showError(`Quick Check Failed: ${error.message}`);
            }
        }

        async function runFullAnalysis() {
            const repositorySlug = document.getElementById('repositorySlug').value;
            const installationId = parseInt(document.getElementById('installationId').value);
            const lookbackDays = parseInt(document.getElementById('lookbackDays').value);
            const minDecisions = parseInt(document.getElementById('minDecisions').value);

            if (!repositorySlug || !installationId) {
                showError('Please fill in Repository Slug and Installation ID');
                return;
            }

            showLoading();

            try {
                const response = await fetch('/api/tech-debt/quality-analysis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        repository_slug: repositorySlug,
                        installation_id: installationId,
                        lookback_days: lookbackDays,
                        min_decisions: minDecisions
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.details || `HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                showResults(data);
            } catch (error) {
                showError(`Full Analysis Failed: ${error.message}`);
            }
        }
    </script>
</body>
</html> 