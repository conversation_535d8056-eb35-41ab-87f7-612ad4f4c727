'use client';

import { useEffect, useState } from 'react';
import { createSupabaseBrowserClient } from '@/lib/supabase-client';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import type { User } from '@supabase/supabase-js';

interface QualityConcern {
  pattern: string;
  examples: string[];
  impact: string;
  frequency: 'high' | 'medium' | 'low';
}

interface RootCause {
  cause: string;
  evidence: string;
  addressable: boolean;
}

interface Recommendation {
  action: string;
  rationale: string;
  priority: 'high' | 'medium' | 'low';
  effort: 'low' | 'medium' | 'high';
}

interface AuthorInsight {
  author: string;
  supersedence_rate: number;
  total_decisions: number;
  superseded_decisions: number;
  quality_concerns: QualityConcern[];
  root_causes: RootCause[];
  recommendations: Recommendation[];
  strengths: string[];
  confidence_score: number;
}

interface RepositoryPattern {
  pattern: string;
  domains_affected: string[];
  evidence: string;
  potential_impact: string;
  recommended_action: string;
}

interface QualityAnalysis {
  summary: string;
  author_insights: AuthorInsight[];
  repository_patterns: RepositoryPattern[];
  analyzed_period: string;
  total_decisions: number;
  authors_analyzed: number;
  authors_with_concerns: number;
}

// Hardcoded repository configuration to prevent network exhaustion
const HARDCODED_REPO = {
  installationId: 0,
  repositorySlug: 'mfts/papermark'
};

export default function QualityAnalysisPage() {
  const [user, setUser] = useState<User | null>(null);
  const [analysis, setAnalysis] = useState<QualityAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedAuthor, setExpandedAuthor] = useState<string | null>(null);

  useEffect(() => {
    const supabase = createSupabaseBrowserClient();
    
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);
    };

    getUser();
  }, []);

  const fetchQualityAnalysis = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/tech-debt/quality-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          repository_slug: HARDCODED_REPO.repositorySlug,
          installation_id: HARDCODED_REPO.installationId,
          lookback_days: 90,
          min_decisions: 3
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || `Failed to fetch analysis: ${response.statusText}`);
      }

      const data = await response.json();
      setAnalysis(data.analysis);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      console.error('Error fetching quality analysis:', err);
    } finally {
      setLoading(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getFrequencyColor = (frequency: string) => {
    switch (frequency) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-orange-100 text-orange-800';
      case 'low': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-gray-600">Please sign in to access decision quality analysis.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Decision Quality Analysis</h1>
        <p className="text-gray-600">
          Constructive insights to improve architectural decision-making patterns
        </p>
      </div>

      {/* Repository Information */}
      <Card className="p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Repository Information</h2>
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm text-gray-600">Repository:</p>
              <p className="font-medium">{HARDCODED_REPO.repositorySlug}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Installation ID:</p>
              <p className="font-medium">{HARDCODED_REPO.installationId}</p>
            </div>
            <Button onClick={fetchQualityAnalysis} disabled={loading}>
              {loading ? 'Analyzing...' : 'Run Analysis'}
            </Button>
          </div>
        </div>
      </Card>

      {/* Loading State */}
      {loading && (
        <Card className="p-6">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
            <div className="space-y-3">
              {[1, 2, 3].map(i => (
                <div key={i} className="h-20 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </Card>
      )}

      {/* Error State */}
              {error && (
          <Card className="p-6 border-red-200 bg-red-50">
            <h3 className="text-lg font-semibold text-red-800 mb-2">Analysis Error</h3>
            <p className="text-red-600 mb-4">{error}</p>
            <Button 
              onClick={fetchQualityAnalysis} 
              variant="outline" 
              size="sm"
            >
              Retry Analysis
            </Button>
          </Card>
        )}

              {/* Analysis Results */}
        {analysis && (
        <div className="space-y-6">
          {/* Summary Card */}
          <Card className="p-6">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-lg font-semibold">Analysis Summary</h3>
              <Button 
                onClick={fetchQualityAnalysis} 
                variant="outline" 
                size="sm"
              >
                Refresh Analysis
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{analysis.total_decisions}</div>
                <div className="text-sm text-gray-600">Total Decisions</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{analysis.authors_analyzed}</div>
                <div className="text-sm text-gray-600">Authors Analyzed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{analysis.authors_with_concerns}</div>
                <div className="text-sm text-gray-600">Growth Opportunities</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{analysis.analyzed_period}</div>
                <div className="text-sm text-gray-600">Analysis Period</div>
              </div>
            </div>
            <p className="text-gray-700">{analysis.summary}</p>
          </Card>

          {/* Author Insights */}
          {analysis.author_insights.length > 0 && (
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Individual Growth Opportunities</h3>
              <p className="text-gray-600 mb-4">
                Constructive insights to help team members improve their architectural decision-making
              </p>
              
              <div className="space-y-4">
                {analysis.author_insights.map((insight, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex justify-between items-center mb-3">
                      <div className="flex items-center space-x-3">
                        <h4 className="font-medium text-gray-900">{insight.author}</h4>
                        <Badge variant="outline">
                          {insight.superseded_decisions}/{insight.total_decisions} superseded ({insight.supersedence_rate}%)
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          Confidence: {Math.round(insight.confidence_score * 100)}%
                        </Badge>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setExpandedAuthor(
                          expandedAuthor === insight.author ? null : insight.author
                        )}
                      >
                        {expandedAuthor === insight.author ? 'Collapse' : 'View Details'}
                      </Button>
                    </div>

                    {expandedAuthor === insight.author && (
                      <div className="space-y-4 mt-4 border-t pt-4">
                        {/* Strengths */}
                        {insight.strengths.length > 0 && (
                          <div>
                            <h5 className="font-medium text-green-800 mb-2">Strengths to Build On</h5>
                            <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
                              {insight.strengths.map((strength, i) => (
                                <li key={i}>{strength}</li>
                              ))}
                            </ul>
                          </div>
                        )}

                        {/* Quality Concerns */}
                        {insight.quality_concerns.length > 0 && (
                          <div>
                            <h5 className="font-medium text-orange-800 mb-2">Areas for Improvement</h5>
                            <div className="space-y-2">
                              {insight.quality_concerns.map((concern, i) => (
                                <div key={i} className="bg-orange-50 p-3 rounded">
                                  <div className="flex justify-between items-start mb-2">
                                    <span className="font-medium text-orange-900">{concern.pattern}</span>
                                    <Badge className={getFrequencyColor(concern.frequency)}>
                                      {concern.frequency}
                                    </Badge>
                                  </div>
                                  <p className="text-sm text-orange-800 mb-2">{concern.impact}</p>
                                  {concern.examples.length > 0 && (
                                    <div className="text-xs text-orange-700">
                                      Examples: {concern.examples.join(', ')}
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Recommendations */}
                        {insight.recommendations.length > 0 && (
                          <div>
                            <h5 className="font-medium text-blue-800 mb-2">Actionable Recommendations</h5>
                            <div className="space-y-2">
                              {insight.recommendations.map((rec, i) => (
                                <div key={i} className="bg-blue-50 p-3 rounded">
                                  <div className="flex justify-between items-start mb-2">
                                    <span className="font-medium text-blue-900">{rec.action}</span>
                                    <div className="flex space-x-1">
                                      <Badge className={getPriorityColor(rec.priority)}>
                                        {rec.priority} priority
                                      </Badge>
                                      <Badge variant="outline" className="text-xs">
                                        {rec.effort} effort
                                      </Badge>
                                    </div>
                                  </div>
                                  <p className="text-sm text-blue-800">{rec.rationale}</p>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Root Causes */}
                        {insight.root_causes.length > 0 && (
                          <div>
                            <h5 className="font-medium text-purple-800 mb-2">Potential Root Causes</h5>
                            <div className="space-y-2">
                              {insight.root_causes.map((cause, i) => (
                                <div key={i} className="bg-purple-50 p-3 rounded">
                                  <div className="flex justify-between items-start mb-2">
                                    <span className="font-medium text-purple-900">{cause.cause}</span>
                                    {cause.addressable && (
                                      <Badge className="bg-green-100 text-green-800">Addressable</Badge>
                                    )}
                                  </div>
                                  <p className="text-sm text-purple-800">{cause.evidence}</p>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </Card>
          )}

          {/* Repository Patterns */}
          {analysis.repository_patterns.length > 0 && (
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Repository-Wide Patterns</h3>
              <p className="text-gray-600 mb-4">
                Systemic patterns that may indicate process or knowledge gaps
              </p>
              
              <div className="space-y-4">
                {analysis.repository_patterns.map((pattern, index) => (
                  <div key={index} className="border rounded-lg p-4 bg-gray-50">
                    <h4 className="font-medium text-gray-900 mb-2">{pattern.pattern}</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Affected Domains:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {pattern.domains_affected.map((domain, i) => (
                            <Badge key={i} variant="outline" className="text-xs">{domain}</Badge>
                          ))}
                        </div>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Evidence:</span>
                        <p className="text-gray-600 mt-1">{pattern.evidence}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Potential Impact:</span>
                        <p className="text-gray-600 mt-1">{pattern.potential_impact}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Recommended Action:</span>
                        <p className="text-blue-700 mt-1 font-medium">{pattern.recommended_action}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          )}

          {/* No Issues Found */}
          {analysis.author_insights.length === 0 && analysis.repository_patterns.length === 0 && (
            <Card className="p-6 text-center">
              <div className="text-green-600 text-lg font-medium mb-2">
                🎉 Excellent Decision Quality!
              </div>
              <p className="text-gray-600">
                No concerning patterns detected in the analyzed timeframe. 
                Your team is making solid architectural decisions.
              </p>
            </Card>
          )}
        </div>
      )}

              {/* No Analysis Yet */}
        {!analysis && !loading && !error && (
          <Card className="p-6 text-center">
            <div className="text-gray-500 text-lg mb-2">Ready to Analyze</div>
            <p className="text-gray-400">
              Click "Run Analysis" above to analyze decision quality patterns for {HARDCODED_REPO.repositorySlug}.
            </p>
          </Card>
        )}
    </div>
  );
} 