'use client';

import { useEffect, useState } from 'react';
import { RepositorySelector } from '@/components/RepositorySelector';
import { createSupabaseBrowserClient } from '@/lib/supabase-client';
import type { User } from '@supabase/supabase-js';

interface TechDebtData {
  id: string;
  pr_title: string;
  pr_url: string;
  friction_score: number;
  friction_summary: string;
  analyzed_at: string;
  user_id: string; 
  friction_details: { file_path: string; description: string; }[];
}

interface StrategicInitiative {
  id: string;
  title: string;
  root_cause_summary: string;
  proposed_initiative: string;
  quantified_impact: {
    affected_pr_count: number;
    average_friction_score: number;
  };
}

interface FeedbackProps {
  analysisId: string;
  userId: string;
}

interface AnalysisTask {
  title: string;
  description: string;
  affected_files?: string[];
}

interface AnalysisDetailItem {
  file_path: string;
  description: string;
}

interface AnalysisResult {
  technical_debt_friction_score: number;
  friction_summary: string;
  friction_details?: AnalysisDetailItem[];
  self_healing_tasks?: AnalysisTask[];
  would_improve_velocity?: boolean;
  velocity_impact_reason?: string;
  confidence_score?: number;
}

const FeedbackComponent = ({ analysisId, userId }: FeedbackProps) => {
  const [feedbackSent, setFeedbackSent] = useState(false);

  const submitFeedback = async (isAccurate: boolean) => {
    try {
      await fetch('/api/tech-debt/feedback', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          analysis_id: analysisId,
          user_id: userId,
          is_accurate: isAccurate,
        }),
      });
      setFeedbackSent(true);
    } catch (error) {
      console.error('Failed to submit feedback', error);
    }
  };

  if (feedbackSent) {
    return <span className="text-sm text-gray-500">Thanks for your feedback!</span>;
  }

  return (
    <div>
      <button onClick={() => submitFeedback(true)} className="text-sm p-1 rounded border hover:bg-gray-100">👍 Accurate</button>
      <button onClick={() => submitFeedback(false)} className="text-sm p-1 rounded border hover:bg-gray-100 ml-2">👎 Inaccurate</button>
    </div>
  );
};

export default function TechDebtDashboard() {
  const [analysisType, setAnalysisType] = useState<'public' | 'private' | null>(null);
  const [publicRepoInput, setPublicRepoInput] = useState('');
  const [selectedRepo, setSelectedRepo] = useState<string | null>(null);
  const [selectedInstallationId, setSelectedInstallationId] = useState<string | null>(null);
  const [data, setData] = useState<TechDebtData[]>([]);
  const [initiatives, setInitiatives] = useState<StrategicInitiative[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [prToAnalyze, setPrToAnalyze] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [lastAnalysisResult, setLastAnalysisResult] = useState<AnalysisResult | null>(null);

  // Use the browser client for client-side operations
  const supabase = createSupabaseBrowserClient();

  useEffect(() => {
    const fetchUser = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setUser(session?.user ?? null);
    };
    fetchUser();
  }, [supabase]);

  useEffect(() => {
    if (selectedRepo) {
      const fetchData = async () => {
        setLoading(true);
        setError(null);
        try {
          // Fetch raw PR data
          const prDataResponse = await fetch(`/api/tech-debt?repository_slug=${selectedRepo}`);
          if (!prDataResponse.ok) throw new Error('Failed to fetch PR data');
          const prData = await prDataResponse.json();
          setData(prData);

          // Fetch strategic initiatives
          // Note: The current initiative API is not repo-specific, this is a simplification
          const initiativesResponse = await fetch('/api/tech-debt/strategic-initiatives');
          if (!initiativesResponse.ok) throw new Error('Failed to fetch strategic initiatives');
          const initiativesData = await initiativesResponse.json();
          setInitiatives(initiativesData);

        } catch (err: unknown) {
          if (err instanceof Error) {
            setError(err.message);
          } else {
            setError('An unknown error occurred');
          }
        } finally {
          setLoading(false);
        }
      };

      fetchData();
    }
  }, [selectedRepo]);

  const handleAnalyzePr = async () => {
    if (!prToAnalyze || !selectedRepo || (!selectedInstallationId && analysisType !== 'public')) {
      alert('Please select a repository and enter a PR number.');
      return;
    }
    setIsAnalyzing(true);
    setLastAnalysisResult(null);
    try {
      const response = await fetch('/api/tech-debt/analyze-pr', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          repository_slug: selectedRepo,
          pr_number: parseInt(prToAnalyze, 10),
          installation_id: analysisType === 'public' ? undefined : parseInt(String(selectedInstallationId), 10),
          is_public: analysisType === 'public',
        }),
      });
      const result = await response.json();
      if (!response.ok) throw new Error(result.error || 'Failed to analyze PR');
      setLastAnalysisResult(result.analysisResult as AnalysisResult);
      // Refresh data in the background
      setSelectedRepo((repo: string | null) => repo ? `${repo} ` : null);
      setSelectedRepo(selectedRepo);

    } catch (err: unknown) {
      const message = err instanceof Error ? err.message : 'An unknown error occurred';
      alert(`Analysis failed: ${message}`);
    } finally {
      setIsAnalyzing(false);
      setPrToAnalyze('');
    }
  };

  const handleAnalyzeThemes = async () => {
    // Note: This trigger currently uses a simplified clustering model on the backend.
    // It will group all recent PRs into a single theme for demonstration.
    setIsAnalyzing(true);
    try {
      await fetch('/api/tech-debt/analyze-themes');
      alert('Thematic analysis triggered. It will run in the background. Refresh in a few minutes to see results.');
    } catch (err: unknown) {
       const message = err instanceof Error ? err.message : 'An unknown error occurred';
       alert(`Failed to trigger thematic analysis: ${message}`);
    } finally {
       setIsAnalyzing(false);
    }
  };

  // Placeholder for aggregated data calculation
  const averageFrictionScore = data.length > 0
    ? (data.reduce((acc: number, item: TechDebtData) => acc + item.friction_score, 0) / data.length).toFixed(1)
    : 'N/A';

  const topInitiative = initiatives[0];

  const calculateHotspots = () => {
    if (data.length === 0) return 'N/A';
    const fileCounts = data.reduce((acc: Record<string, number>, item: TechDebtData) => {
      item.friction_details?.forEach((detail: { file_path: string }) => {
        acc[detail.file_path] = (acc[detail.file_path] || 0) + 1;
      });
      return acc;
    }, {} as Record<string, number>);

    const topEntry = (Object.entries(fileCounts).sort((a, b) => b[1] - a[1])[0]) as [string, number] | undefined;
    return topEntry ? topEntry[0] : 'N/A';
  };
  const topHotspot = calculateHotspots();

  return (
    <div className="container mx-auto p-4 dark:text-gray-100 dark:bg-zinc-900">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold dark:text-gray-100">Technical Debt Dashboard</h1>
        <div className="flex space-x-4">
          <a
            href="/tech-debt-dashboard"
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-zinc-800 dark:text-gray-100 dark:border-zinc-600 dark:hover:bg-zinc-700"
          >
            Friction Analysis
          </a>
          <a
            href="/tech-debt-dashboard/quality-analysis"
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
          >
            Quality Analysis
          </a>
        </div>
      </div>

      {/* Analysis Type Selection */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border rounded-lg bg-gray-50 dark:bg-zinc-800 dark:border-zinc-700">
        <button
          className={`${analysisType === 'public' ? 'border-blue-500 bg-blue-50 text-blue-600 ring-2 ring-blue-600' : 'border-gray-300 bg-white text-gray-900 hover:bg-gray-50 dark:border-zinc-600 dark:bg-zinc-800 dark:text-gray-100 dark:hover:bg-zinc-700'} flex flex-col items-center justify-center rounded-lg border p-4 text-center`}
          onClick={() => {
            setAnalysisType('public');
            setSelectedInstallationId('0');
            setSelectedRepo(publicRepoInput.includes('/') ? publicRepoInput.trim() : null);
          }}
        >
          <span className="text-lg font-medium">Public Repository</span>
        </button>
        <button
          className={`${analysisType === 'private' ? 'border-blue-500 bg-blue-50 text-blue-600 ring-2 ring-blue-600' : 'border-gray-300 bg-white text-gray-900 hover:bg-gray-50 dark:border-zinc-600 dark:bg-zinc-800 dark:text-gray-100 dark:hover:bg-zinc-700'} flex flex-col items-center justify-center rounded-lg border p-4 text-center`}
          onClick={() => {
            setAnalysisType('private');
            setSelectedInstallationId(null);
            setSelectedRepo(null);
          }}
        >
          <span className="text-lg font-medium">Private Repository</span>
        </button>
      </div>

      {/* Public repo input or private selector */}
      {analysisType === 'public' ? (
        <div className="grid grid-cols-1 gap-4 p-4 border border-gray-200 rounded-lg mt-4 dark:border-zinc-700 dark:bg-zinc-800">
          <label htmlFor="publicRepo" className="block text-sm font-medium dark:text-gray-300">Public Repository</label>
          <div className="flex items-center gap-2">
            <input
              id="publicRepo"
              type="text"
              value={publicRepoInput}
              onChange={(e) => setPublicRepoInput(e.target.value)}
              onKeyDown={(e) => {
                if ((e as React.KeyboardEvent<HTMLInputElement>).key === 'Enter') {
                  const slug = publicRepoInput.trim();
                  setSelectedRepo(slug.includes('/') ? slug : null);
                  e.preventDefault();
                }
              }}
              placeholder="owner/repo (e.g., facebook/react)"
              className="border p-2 rounded flex-1 dark:bg-zinc-800 dark:text-gray-100 dark:border-zinc-600"
              disabled={isAnalyzing}
            />
            <button
              onClick={() => {
                const slug = publicRepoInput.trim();
                setSelectedRepo(slug.includes('/') ? slug : null);
              }}
              className="p-2 rounded border dark:border-zinc-600 dark:text-gray-100 dark:bg-zinc-800"
              disabled={isAnalyzing}
            >
              Select
            </button>
          </div>
        </div>
      ) : analysisType === 'private' ? (
        <RepositorySelector onSelectionChange={({ repositorySlug, installationId }) => {
          setSelectedRepo(repositorySlug);
          setSelectedInstallationId(installationId);
        }} />
      ) : null}

      {selectedRepo && (
        <>
          <div className="mt-8 p-4 border rounded-lg bg-gray-50 dark:bg-zinc-800 dark:border-zinc-700">
            <h3 className="text-lg font-semibold">Quality Assessment Tools</h3>
            <div className="flex items-center gap-4 mt-2">
              <input
                type="text"
                value={prToAnalyze}
                onChange={(e) => setPrToAnalyze(e.target.value)}
                placeholder="Enter PR #"
                className="border p-2 rounded dark:bg-zinc-800 dark:text-gray-100 dark:border-zinc-600"
                disabled={isAnalyzing}
              />
              <button
                onClick={handleAnalyzePr}
                className="bg-blue-500 text-white p-2 rounded hover:bg-blue-600 disabled:bg-gray-400"
                disabled={isAnalyzing || !prToAnalyze}
              >
                {isAnalyzing ? 'Analyzing...' : 'Analyze Single PR'}
              </button>
              <button
                onClick={handleAnalyzeThemes}
                className="bg-purple-500 text-white p-2 rounded hover:bg-purple-600 disabled:bg-gray-400"
                disabled={isAnalyzing}
              >
                {isAnalyzing ? 'Analyzing...' : 'Run Thematic Analysis'}
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-2 dark:text-gray-400">
              Use these tools to manually trigger analysis for quality validation before enabling automated runs.
            </p>

            {lastAnalysisResult && (
              <div className="mt-4 p-4 bg-white rounded border dark:bg-zinc-800 dark:text-gray-100 dark:border-zinc-700">
                <h4 className="text-md font-semibold mb-2">Latest PR Analysis</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">Friction Score</div>
                    <div className="text-2xl font-bold">{lastAnalysisResult.technical_debt_friction_score}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">Velocity Impact</div>
                    <div className="text-base">
                      {typeof lastAnalysisResult.would_improve_velocity === 'boolean' ? (
                        <span>
                          {lastAnalysisResult.would_improve_velocity ? 'Would improve' : 'Unlikely to improve'}
                          {lastAnalysisResult.velocity_impact_reason ? ` — ${lastAnalysisResult.velocity_impact_reason}` : ''}
                        </span>
                      ) : (
                        <span className="text-gray-400">N/A</span>
                      )}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">Confidence</div>
                    <div className="text-base">
                      {typeof lastAnalysisResult.confidence_score === 'number' ? `${Math.round(lastAnalysisResult.confidence_score * 100)}%` : 'N/A'}
                    </div>
                  </div>
                </div>

                <div className="mt-3">
                  <div className="text-sm text-gray-500 dark:text-gray-400">Summary</div>
                  <div className="text-sm">{lastAnalysisResult.friction_summary}</div>
                </div>

                {lastAnalysisResult.friction_details && lastAnalysisResult.friction_details.length > 0 && (
                  <div className="mt-3">
                    <div className="text-sm font-medium">Friction Details</div>
                    <ul className="list-disc pl-6 text-sm">
                      {lastAnalysisResult.friction_details.map((d, idx) => (
                        <li key={idx}><span className="font-mono">{d.file_path}</span>: {d.description}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {lastAnalysisResult.self_healing_tasks && lastAnalysisResult.self_healing_tasks.length > 0 && (
                  <div className="mt-3">
                    <div className="text-sm font-medium">Self-Healing Tasks</div>
                    <ul className="list-disc pl-6 text-sm">
                      {lastAnalysisResult.self_healing_tasks.map((t, idx) => (
                        <li key={idx}>
                          <span className="font-medium">{t.title}</span>: {t.description}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>

          <div className="mt-8">
            <h2 className="text-xl font-semibold">Repository: {selectedRepo}</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 my-4">
              <div className="p-4 bg-white rounded shadow dark:bg-zinc-800 dark:text-gray-100">
                <h3 className="text-lg font-medium">Average Friction Score</h3>
                <p className="text-3xl font-bold">{averageFrictionScore}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Based on {data.length} PRs</p>
              </div>
              <div className="p-4 bg-white rounded shadow dark:bg-zinc-800 dark:text-gray-100">
                <h3 className="text-lg font-medium">Top Friction Hotspot</h3>
                <p className="text-xl font-bold truncate" title={topHotspot}>{topHotspot}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Most frequent source of friction</p>
              </div>
              <div className="p-4 bg-white rounded shadow dark:bg-zinc-800 dark:text-gray-100">
                <h3 className="text-lg font-medium">Top Strategic Initiative</h3>
                {topInitiative ? (
                  <div>
                    <p className="text-xl font-bold">{topInitiative.title}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Affecting {topInitiative.quantified_impact.affected_pr_count} PRs with avg. score of {topInitiative.quantified_impact.average_friction_score}
                    </p>
                  </div>
                ) : (
                  <p className="text-xl font-bold">Analysis Pending</p>
                )}
              </div>
            </div>

            {loading && <p>Loading...</p>}
            {error && <p className="text-red-500">{error}</p>}
            
            {/* Placeholder for Raw PR Data Table */}
            <div className="mt-8">
               <h3 className="text-lg font-semibold mb-2">Recent PR Analyses</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full bg-white rounded shadow dark:bg-zinc-800 dark:text-gray-100">
                  <thead>
                    <tr className="w-full h-16 border-gray-300 border-b py-8 dark:border-zinc-700">
                      <th className="text-left pl-4 dark:text-gray-300">Friction Score</th>
                      <th className="text-left dark:text-gray-300">PR Title</th>
                      <th className="text-left dark:text-gray-300">Summary</th>
                      <th className="text-left pr-4 dark:text-gray-300">Analyzed At</th>
                      <th className="text-left pr-4 dark:text-gray-300">Feedback</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.map((item: TechDebtData) => (
                      <tr key={item.id} className="h-14 border-gray-300 border-b dark:border-zinc-700">
                        <td className="pl-4">{item.friction_score}</td>
                        <td><a href={item.pr_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline">{item.pr_title}</a></td>
                        <td>{item.friction_summary}</td>
                        <td className="pr-4">{new Date(item.analyzed_at).toLocaleDateString()}</td>
                        <td className="pr-4">
                          <FeedbackComponent analysisId={item.id} userId={user?.id || ''} />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
} 