import { NextRequest, NextResponse } from 'next/server';
import { 
  getRepositorySettings, 
  updateRepositorySettings,
  deleteRepositorySettings 
} from '@/services/repositorySettingsService';

interface RouteParams {
  params: {
    slug: string;
  };
}

// GET /api/repository-settings/[slug]
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Decode the slug (it might be URL encoded)
    const repositorySlug = decodeURIComponent(params.slug);

    const { data, error } = await getRepositorySettings(repositorySlug);
    
    if (error) {
      return NextResponse.json({ error: 'Failed to fetch repository settings' }, { status: 500 });
    }

    return NextResponse.json({ settings: data });
  } catch (error) {
    console.error('Error in GET /api/repository-settings/[slug]:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT /api/repository-settings/[slug]
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const repositorySlug = decodeURIComponent(params.slug);
    const body = await request.json();
    const { max_commits_deep_analysis, tech_debt_analysis_enabled } = body;

    // Validate max_commits_deep_analysis
    if (max_commits_deep_analysis !== undefined) {
      if (typeof max_commits_deep_analysis !== 'number' || max_commits_deep_analysis < 1) {
        return NextResponse.json({ 
          error: 'max_commits_deep_analysis must be a positive number' 
        }, { status: 400 });
      }
    }

    const { data, error } = await updateRepositorySettings(repositorySlug, {
      max_commits_deep_analysis,
      tech_debt_analysis_enabled
    });

    if (error) {
      return NextResponse.json({ error: 'Failed to update repository settings' }, { status: 500 });
    }

    return NextResponse.json({ settings: data });
  } catch (error) {
    console.error('Error in PUT /api/repository-settings/[slug]:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE /api/repository-settings/[slug]
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const repositorySlug = decodeURIComponent(params.slug);

    const { error } = await deleteRepositorySettings(repositorySlug);

    if (error) {
      return NextResponse.json({ error: 'Failed to delete repository settings' }, { status: 500 });
    }

    return NextResponse.json({ message: 'Repository settings deleted successfully' });
  } catch (error) {
    console.error('Error in DELETE /api/repository-settings/[slug]:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 