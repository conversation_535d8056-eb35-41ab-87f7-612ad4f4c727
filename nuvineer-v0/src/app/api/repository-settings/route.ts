import { NextRequest, NextResponse } from 'next/server';
import { 
  createRepositorySettings, 
  getRepositorySettings, 
  upsertRepositorySettings,
  getAllRepositorySettings 
} from '@/services/repositorySettingsService';

// GET /api/repository-settings?repository_slug=owner/repo
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const repositorySlug = searchParams.get('repository_slug');

    if (!repositorySlug) {
      // If no repository_slug provided, return all settings (admin use)
      const { data, error } = await getAllRepositorySettings();
      
      if (error) {
        return NextResponse.json({ error: 'Failed to fetch repository settings' }, { status: 500 });
      }

      return NextResponse.json({ settings: data });
    }

    const { data, error } = await getRepositorySettings(repositorySlug);
    
    if (error) {
      return NextResponse.json({ error: 'Failed to fetch repository settings' }, { status: 500 });
    }

    return NextResponse.json({ settings: data });
  } catch (error) {
    console.error('Error in GET /api/repository-settings:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/repository-settings
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { repository_slug, max_commits_deep_analysis, tech_debt_analysis_enabled } = body;

    if (!repository_slug) {
      return NextResponse.json({ error: 'repository_slug is required' }, { status: 400 });
    }

    // Validate max_commits_deep_analysis
    if (max_commits_deep_analysis !== undefined) {
      if (typeof max_commits_deep_analysis !== 'number' || max_commits_deep_analysis < 1) {
        return NextResponse.json({ 
          error: 'max_commits_deep_analysis must be a positive number' 
        }, { status: 400 });
      }
    }

    // Use upsert to handle both create and update scenarios
    const { data, error } = await upsertRepositorySettings({
      repository_slug,
      max_commits_deep_analysis,
      tech_debt_analysis_enabled
    });

    if (error) {
      return NextResponse.json({ error: 'Failed to save repository settings' }, { status: 500 });
    }

    return NextResponse.json({ settings: data });
  } catch (error) {
    console.error('Error in POST /api/repository-settings:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 