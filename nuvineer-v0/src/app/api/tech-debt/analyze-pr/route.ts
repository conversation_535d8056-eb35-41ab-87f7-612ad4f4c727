import { NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase-server';
import { fetchAndFormatPRData } from '@/lib/github';
import { getOctokit } from '@/lib/github-auth';
import { analyzeTechnicalDebt } from '@/orchestrator';

export async function POST(request: Request) {
  const supabase = createSupabaseAdminClient();
  const body = await request.json();
  const {
    repository_slug,
    pr_number,
    installation_id,
    is_public,
    isPublic,
  } = body as {
    repository_slug?: string;
    pr_number?: number;
    installation_id?: number | string;
    is_public?: boolean;
    isPublic?: boolean;
  };

  const isPublicRepo = Boolean(is_public ?? isPublic);

  if (!repository_slug || !pr_number || (!installation_id && !isPublicRepo)) {
    return NextResponse.json(
      { error: 'repository_slug, pr_number, and either installation_id or is_public=true are required' },
      { status: 400 }
    );
  }

  const [owner, repo] = repository_slug.split('/');

  try {
    // 1. Fetch PR data from GitHub
    console.log(`[API AnalyzePR] Fetching data for PR #${pr_number}...`);
    const octokit = await getOctokit(
      isPublicRepo ? undefined : String(installation_id)
    );
    const { formattedData, codeChanges } = await fetchAndFormatPRData(octokit, owner, repo, pr_number);
    const { prContext, formattedComments } = formattedData;

    // 2. Fetch architectural context
    console.log(`[API AnalyzePR] Fetching architectural context...`);
    // This part is simplified from the main orchestrator for now
    const architecturalContext = 'Context retrieval for single PR analysis is a future enhancement.';

    // 3. Run the analysis
    console.log(`[API AnalyzePR] Running technical debt analysis...`);
    const analysisResult = await analyzeTechnicalDebt(prContext, codeChanges, formattedComments, repository_slug, architecturalContext);

    if (analysisResult.error) {
        throw new Error(analysisResult.message || 'Analysis failed');
    }

    console.log(`[API AnalyzePR] Analysis complete. Friction score: ${analysisResult.technical_debt_friction_score}`);
    return NextResponse.json({ success: true, analysisResult });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error(`[API AnalyzePR] Error processing PR #${pr_number}:`, errorMessage);
    return NextResponse.json({ error: `Failed to analyze PR #${pr_number}: ${errorMessage}` }, { status: 500 });
  }
} 