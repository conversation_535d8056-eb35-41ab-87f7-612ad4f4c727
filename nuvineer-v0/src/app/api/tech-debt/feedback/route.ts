import { createSupabaseAdminClient } from '@/lib/supabase-server';
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  const supabase = createSupabaseAdminClient();
  const { analysis_id, user_id, is_accurate, comment } = await request.json();

  if (!analysis_id || !user_id || is_accurate === undefined) {
    return NextResponse.json({ error: 'analysis_id, user_id, and is_accurate are required' }, { status: 400 });
  }

  try {
    const { data, error } = await supabase
      .from('technical_debt_feedback')
      .insert({
        analysis_id,
        user_id,
        is_accurate,
        comment,
      });

    if (error) {
      console.error('Error submitting feedback:', error);
      throw error;
    }

    return NextResponse.json({ success: true, data });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to submit feedback' }, { status: 500 });
  }
} 