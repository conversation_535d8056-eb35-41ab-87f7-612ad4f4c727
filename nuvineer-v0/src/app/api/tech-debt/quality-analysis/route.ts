import { NextRequest, NextResponse } from 'next/server';

interface QualityAnalysisResult {
    summary: string;
    author_insights: any[];
    repository_patterns: any[];
    analyzed_period: string;
    total_decisions: number;
    authors_analyzed: number;
    authors_with_concerns: number;
}

/**
 * POST /api/tech-debt/quality-analysis
 * Analyzes decision quality patterns for a repository
 */
export async function POST(request: NextRequest) {
    const logPrefix = '[API Decision Quality Analysis]';
    console.log(`${logPrefix} Received quality analysis request`);

    try {
        // Dynamic import to avoid build-time issues
        const { analyzeDecisionQuality } = await import('../../../../orchestrator.js');
        const { getRepositoryNamespace } = await import('../../../../lib/pinecone-utils');
        
        const body = await request.json();
        const { repository_slug, installation_id, lookback_days = 90, min_decisions = 3 } = body;

        if (!repository_slug || !installation_id) {
            return NextResponse.json(
                { error: 'Missing required fields: repository_slug, installation_id' },
                { status: 400 }
            );
        }

        console.log(`${logPrefix} Analyzing quality for repository: ${repository_slug}`);

        // Get the correct namespace for the repository
        const namespace = getRepositoryNamespace(installation_id, repository_slug);
        console.log(`${logPrefix} Using namespace: ${namespace}`);

        // Perform the quality analysis
        const analysisResult = await analyzeDecisionQuality(
            repository_slug,
            namespace,
            {
                lookbackDays: lookback_days,
                minDecisionsForAnalysis: min_decisions
            }
        ) as QualityAnalysisResult;

        console.log(`${logPrefix} Analysis completed successfully`);

        return NextResponse.json({
            success: true,
            repository_slug,
            analysis: analysisResult,
            generated_at: new Date().toISOString()
        });

    } catch (error) {
        console.error(`${logPrefix} Error during quality analysis:`, error);
        
        return NextResponse.json(
            { 
                error: 'Failed to analyze decision quality',
                details: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
}

/**
 * GET /api/tech-debt/quality-analysis?repository_slug=owner/repo&installation_id=123
 * Quick quality check endpoint
 */
export async function GET(request: NextRequest) {
    const logPrefix = '[API Decision Quality Check]';
    const { searchParams } = new URL(request.url);
    
    const repository_slug = searchParams.get('repository_slug');
    const installation_id = searchParams.get('installation_id');
    
    if (!repository_slug || !installation_id) {
        return NextResponse.json(
            { error: 'Missing required query parameters: repository_slug, installation_id' },
            { status: 400 }
        );
    }

    try {
        // Dynamic import to avoid build-time issues
        const { analyzeDecisionQuality } = await import('../../../../orchestrator.js');
        const { getRepositoryNamespace } = await import('../../../../lib/pinecone-utils');
        
        console.log(`${logPrefix} Quick quality check for: ${repository_slug}`);

        const namespace = getRepositoryNamespace(parseInt(installation_id), repository_slug);
        
        // Quick analysis with shorter lookback for GET requests
        const analysisResult = await analyzeDecisionQuality(
            repository_slug,
            namespace,
            {
                lookbackDays: 30,  // Shorter lookback for quick checks
                minDecisionsForAnalysis: 2
            }
        ) as QualityAnalysisResult;

        // Return summary-only view for GET requests
        const summary = {
            repository_slug,
            has_quality_concerns: analysisResult.author_insights.length > 0 || analysisResult.repository_patterns.length > 0,
            total_decisions: analysisResult.total_decisions,
            authors_analyzed: analysisResult.authors_analyzed,
            authors_with_concerns: analysisResult.authors_with_concerns,
            analyzed_period: analysisResult.analyzed_period,
            summary: analysisResult.summary
        };

        return NextResponse.json({
            success: true,
            ...summary,
            generated_at: new Date().toISOString()
        });

    } catch (error) {
        console.error(`${logPrefix} Error during quality check:`, error);
        
        return NextResponse.json(
            { 
                error: 'Failed to check decision quality',
                details: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
} 