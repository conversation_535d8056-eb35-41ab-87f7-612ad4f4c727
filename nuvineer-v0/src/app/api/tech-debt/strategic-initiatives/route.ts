import { createSupabaseAdminClient } from '@/lib/supabase-server';
import { NextResponse } from 'next/server';

export async function GET() {
  const supabase = createSupabaseAdminClient();

  try {
    const { data, error } = await supabase
      .from('strategic_initiatives')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10); // Return the 10 most recent initiatives

    if (error) {
      console.error('Error fetching strategic initiatives:', error);
      throw error;
    }

    return NextResponse.json(data);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch strategic initiatives' }, { status: 500 });
  }
} 