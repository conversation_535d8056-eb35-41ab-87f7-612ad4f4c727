import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export interface RepositorySettings {
  id?: string;
  repository_slug: string;
  max_commits_deep_analysis: number;
  tech_debt_analysis_enabled: boolean;
  vector_db?: 'pinecone' | 'pgvector';
  created_at?: string;
  updated_at?: string;
}

export interface CreateRepositorySettingsRequest {
  repository_slug: string;
  max_commits_deep_analysis?: number;
  tech_debt_analysis_enabled?: boolean;
  vector_db?: 'pinecone' | 'pgvector';
}

export interface UpdateRepositorySettingsRequest {
  max_commits_deep_analysis?: number;
  tech_debt_analysis_enabled?: boolean;
  vector_db?: 'pinecone' | 'pgvector';
}

/**
 * Create repository settings for a new repository
 */
export async function createRepositorySettings(
  settings: CreateRepositorySettingsRequest
): Promise<{ data: RepositorySettings | null; error: any }> {
  const { data, error } = await supabase
    .from('repository_settings')
    .insert({
      repository_slug: settings.repository_slug,
      max_commits_deep_analysis: settings.max_commits_deep_analysis ?? 500,
      tech_debt_analysis_enabled: settings.tech_debt_analysis_enabled ?? false,
      vector_db: settings.vector_db ?? 'pgvector',
      updated_at: new Date().toISOString()
    })
    .select()
    .single();

  if (error) {
    console.error('Error creating repository settings:', error);
    return { data: null, error };
  }

  return { data, error: null };
}

/**
 * Get repository settings by repository slug
 */
export async function getRepositorySettings(
  repositorySlug: string
): Promise<{ data: RepositorySettings | null; error: any }> {
  const { data, error } = await supabase
    .from('repository_settings')
    .select('*')
    .eq('repository_slug', repositorySlug)
    .single();

  if (error && error.code !== 'PGRST116') {
    console.error('Error fetching repository settings:', error);
    return { data: null, error };
  }

  // Return default settings if none found
  if (!data) {
    return {
      data: {
        repository_slug: repositorySlug,
        max_commits_deep_analysis: 500,
        tech_debt_analysis_enabled: false,
        vector_db: 'pgvector'
      },
      error: null
    };
  }

  return { data, error: null };
}

/**
 * Update repository settings
 */
export async function updateRepositorySettings(
  repositorySlug: string,
  updates: UpdateRepositorySettingsRequest
): Promise<{ data: RepositorySettings | null; error: any }> {
  const { data, error } = await supabase
    .from('repository_settings')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('repository_slug', repositorySlug)
    .select()
    .single();

  if (error) {
    console.error('Error updating repository settings:', error);
    return { data: null, error };
  }

  return { data, error: null };
}

/**
 * Upsert repository settings (create if doesn't exist, update if exists)
 */
export async function upsertRepositorySettings(
  settings: CreateRepositorySettingsRequest
): Promise<{ data: RepositorySettings | null; error: any }> {
  const { data, error } = await supabase
    .from('repository_settings')
    .upsert({
      repository_slug: settings.repository_slug,
      max_commits_deep_analysis: settings.max_commits_deep_analysis ?? 500,
      tech_debt_analysis_enabled: settings.tech_debt_analysis_enabled ?? false,
      vector_db: settings.vector_db ?? 'pgvector',
      updated_at: new Date().toISOString()
    }, {
      onConflict: 'repository_slug'
    })
    .select()
    .single();

  if (error) {
    console.error('Error upserting repository settings:', error);
    return { data: null, error };
  }

  return { data, error: null };
}

/**
 * Delete repository settings
 */
export async function deleteRepositorySettings(
  repositorySlug: string
): Promise<{ error: any }> {
  const { error } = await supabase
    .from('repository_settings')
    .delete()
    .eq('repository_slug', repositorySlug);

  if (error) {
    console.error('Error deleting repository settings:', error);
  }

  return { error };
}

/**
 * Get all repository settings (for admin use)
 */
export async function getAllRepositorySettings(): Promise<{
  data: RepositorySettings[] | null;
  error: any;
}> {
  const { data, error } = await supabase
    .from('repository_settings')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching all repository settings:', error);
    return { data: null, error };
  }

  return { data, error: null };
} 