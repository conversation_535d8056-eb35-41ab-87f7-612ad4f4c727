import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface QualityConcern {
  pattern: string;
  examples: string[];
  impact: string;
  frequency: 'high' | 'medium' | 'low';
}

interface RootCause {
  cause: string;
  evidence: string;
  addressable: boolean;
}

interface Recommendation {
  action: string;
  rationale: string;
  priority: 'high' | 'medium' | 'low';
  effort: 'low' | 'medium' | 'high';
}

interface AuthorInsight {
  author: string;
  supersedence_rate: number;
  total_decisions: number;
  superseded_decisions: number;
  quality_concerns: QualityConcern[];
  root_causes: RootCause[];
  recommendations: Recommendation[];
  strengths: string[];
  confidence_score: number;
}

interface RepositoryPattern {
  pattern: string;
  domains_affected: string[];
  evidence: string;
  potential_impact: string;
  recommended_action: string;
}

interface QualityAnalysis {
  summary: string;
  author_insights: AuthorInsight[];
  repository_patterns: RepositoryPattern[];
  analyzed_period: string;
  total_decisions: number;
  authors_analyzed: number;
  authors_with_concerns: number;
}

interface DecisionQualityDashboardProps {
  repositorySlug: string;
  installationId: number;
}

export default function DecisionQualityDashboard({ repositorySlug, installationId }: DecisionQualityDashboardProps) {
  const [analysis, setAnalysis] = useState<QualityAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedAuthor, setExpandedAuthor] = useState<string | null>(null);

  const fetchQualityAnalysis = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/tech-debt/quality-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          repository_slug: repositorySlug,
          installation_id: installationId,
          lookback_days: 90,
          min_decisions: 3
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch analysis: ${response.statusText}`);
      }

      const data = await response.json();
      setAnalysis(data.analysis);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      console.error('Error fetching quality analysis:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchQualityAnalysis();
  }, [repositorySlug, installationId]);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getFrequencyColor = (frequency: string) => {
    switch (frequency) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-orange-100 text-orange-800';
      case 'low': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Card className="p-4 border-red-200 bg-red-50">
          <h3 className="text-lg font-semibold text-red-800 mb-2">Analysis Error</h3>
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchQualityAnalysis} variant="outline" size="sm">
            Retry Analysis
          </Button>
        </Card>
      </div>
    );
  }

  if (!analysis) {
    return (
      <div className="p-6">
        <Card className="p-4">
          <p className="text-gray-500">No analysis data available</p>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Decision Quality Insights</h2>
          <p className="text-gray-600 mt-1">Constructive feedback for architectural decision-making</p>
        </div>
        <Button onClick={fetchQualityAnalysis} variant="outline" size="sm">
          Refresh Analysis
        </Button>
      </div>

      {/* Summary Card */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Analysis Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{analysis.total_decisions}</div>
            <div className="text-sm text-gray-600">Total Decisions</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{analysis.authors_analyzed}</div>
            <div className="text-sm text-gray-600">Authors Analyzed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">{analysis.authors_with_concerns}</div>
            <div className="text-sm text-gray-600">Growth Opportunities</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{analysis.analyzed_period}</div>
            <div className="text-sm text-gray-600">Analysis Period</div>
          </div>
        </div>
        <p className="text-gray-700">{analysis.summary}</p>
      </Card>

      {/* Author Insights */}
      {analysis.author_insights.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Individual Growth Opportunities</h3>
          <p className="text-gray-600 mb-4">
            Constructive insights to help team members improve their architectural decision-making
          </p>
          
          <div className="space-y-4">
            {analysis.author_insights.map((insight, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex justify-between items-center mb-3">
                  <div className="flex items-center space-x-3">
                    <h4 className="font-medium text-gray-900">{insight.author}</h4>
                    <Badge variant="outline">
                      {insight.superseded_decisions}/{insight.total_decisions} superseded ({insight.supersedence_rate}%)
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      Confidence: {Math.round(insight.confidence_score * 100)}%
                    </Badge>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setExpandedAuthor(
                      expandedAuthor === insight.author ? null : insight.author
                    )}
                  >
                    {expandedAuthor === insight.author ? 'Collapse' : 'View Details'}
                  </Button>
                </div>

                {expandedAuthor === insight.author && (
                  <div className="space-y-4 mt-4 border-t pt-4">
                    {/* Strengths */}
                    {insight.strengths.length > 0 && (
                      <div>
                        <h5 className="font-medium text-green-800 mb-2">Strengths to Build On</h5>
                        <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
                          {insight.strengths.map((strength, i) => (
                            <li key={i}>{strength}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Quality Concerns */}
                    {insight.quality_concerns.length > 0 && (
                      <div>
                        <h5 className="font-medium text-orange-800 mb-2">Areas for Improvement</h5>
                        <div className="space-y-2">
                          {insight.quality_concerns.map((concern, i) => (
                            <div key={i} className="bg-orange-50 p-3 rounded">
                              <div className="flex justify-between items-start mb-2">
                                <span className="font-medium text-orange-900">{concern.pattern}</span>
                                <Badge className={getFrequencyColor(concern.frequency)}>
                                  {concern.frequency}
                                </Badge>
                              </div>
                              <p className="text-sm text-orange-800 mb-2">{concern.impact}</p>
                              {concern.examples.length > 0 && (
                                <div className="text-xs text-orange-700">
                                  Examples: {concern.examples.join(', ')}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Recommendations */}
                    {insight.recommendations.length > 0 && (
                      <div>
                        <h5 className="font-medium text-blue-800 mb-2">Actionable Recommendations</h5>
                        <div className="space-y-2">
                          {insight.recommendations.map((rec, i) => (
                            <div key={i} className="bg-blue-50 p-3 rounded">
                              <div className="flex justify-between items-start mb-2">
                                <span className="font-medium text-blue-900">{rec.action}</span>
                                <div className="flex space-x-1">
                                  <Badge className={getPriorityColor(rec.priority)}>
                                    {rec.priority} priority
                                  </Badge>
                                  <Badge variant="outline" className="text-xs">
                                    {rec.effort} effort
                                  </Badge>
                                </div>
                              </div>
                              <p className="text-sm text-blue-800">{rec.rationale}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Root Causes */}
                    {insight.root_causes.length > 0 && (
                      <div>
                        <h5 className="font-medium text-purple-800 mb-2">Potential Root Causes</h5>
                        <div className="space-y-2">
                          {insight.root_causes.map((cause, i) => (
                            <div key={i} className="bg-purple-50 p-3 rounded">
                              <div className="flex justify-between items-start mb-2">
                                <span className="font-medium text-purple-900">{cause.cause}</span>
                                {cause.addressable && (
                                  <Badge className="bg-green-100 text-green-800">Addressable</Badge>
                                )}
                              </div>
                              <p className="text-sm text-purple-800">{cause.evidence}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Repository Patterns */}
      {analysis.repository_patterns.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Repository-Wide Patterns</h3>
          <p className="text-gray-600 mb-4">
            Systemic patterns that may indicate process or knowledge gaps
          </p>
          
          <div className="space-y-4">
            {analysis.repository_patterns.map((pattern, index) => (
              <div key={index} className="border rounded-lg p-4 bg-gray-50">
                <h4 className="font-medium text-gray-900 mb-2">{pattern.pattern}</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Affected Domains:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {pattern.domains_affected.map((domain, i) => (
                        <Badge key={i} variant="outline" className="text-xs">{domain}</Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Evidence:</span>
                    <p className="text-gray-600 mt-1">{pattern.evidence}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Potential Impact:</span>
                    <p className="text-gray-600 mt-1">{pattern.potential_impact}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Recommended Action:</span>
                    <p className="text-blue-700 mt-1 font-medium">{pattern.recommended_action}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* No Issues Found */}
      {analysis.author_insights.length === 0 && analysis.repository_patterns.length === 0 && (
        <Card className="p-6 text-center">
          <div className="text-green-600 text-lg font-medium mb-2">
            🎉 Excellent Decision Quality!
          </div>
          <p className="text-gray-600">
            No concerning patterns detected in the analyzed timeframe. 
            Your team is making solid architectural decisions.
          </p>
        </Card>
      )}
    </div>
  );
} 