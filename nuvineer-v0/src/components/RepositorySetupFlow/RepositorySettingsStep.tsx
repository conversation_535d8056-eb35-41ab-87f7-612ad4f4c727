import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { RepositorySettings } from '@/types';

interface RepositorySettingsStepProps {
  repositorySlug: string;
  onSettingsSaved: (settings: RepositorySettings) => void;
  onSkip: () => void;
  initialSettings?: RepositorySettings;
}

export function RepositorySettingsStep({
  repositorySlug,
  onSettingsSaved,
  onSkip,
  initialSettings
}: RepositorySettingsStepProps) {
  const [settings, setSettings] = useState<RepositorySettings>({
    repository_slug: repositorySlug,
    max_commits_deep_analysis: initialSettings?.max_commits_deep_analysis || 500,
    tech_debt_analysis_enabled: initialSettings?.tech_debt_analysis_enabled || false,
    vector_db: initialSettings?.vector_db || 'pgvector'
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [inputError, setInputError] = useState<string | null>(null);

  // Validate the max commits input
  const validateMaxCommits = (value: number) => {
    if (value < 1) {
      setInputError('Maximum commits must be at least 1');
      return false;
    }
    if (value > 50000) {
      setInputError('Maximum commits should be reasonable (we suggest keeping it under 50,000 for performance)');
      return false;
    }
    setInputError(null);
    return true;
  };

  const handleMaxCommitsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value) || 0;
    setSettings(prev => ({ ...prev, max_commits_deep_analysis: value }));
    validateMaxCommits(value);
  };

  const handleTechDebtToggle = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSettings(prev => ({ ...prev, tech_debt_analysis_enabled: e.target.checked }));
  };

  const handleSave = async () => {
    if (!validateMaxCommits(settings.max_commits_deep_analysis)) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/repository-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          repository_slug: repositorySlug,
          max_commits_deep_analysis: settings.max_commits_deep_analysis,
          tech_debt_analysis_enabled: settings.tech_debt_analysis_enabled,
          vector_db: settings.vector_db,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save repository settings');
      }

      const result = await response.json();
      onSettingsSaved(result.settings);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while saving settings');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Configure Repository Analysis Settings
        </h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Set up how many commits/PRs you want to analyze with deep LLM analysis for <strong>{repositorySlug}</strong>. 
          This helps control processing time and costs while ensuring you get insights from your most recent work.
        </p>
      </div>

      <Card className="p-6 max-w-2xl mx-auto">
        <div className="space-y-6">
          {/* Max Commits Setting */}
          <div>
            <label htmlFor="max-commits" className="block text-sm font-medium text-gray-700 mb-2">
              Maximum Commits/PRs for Deep Analysis
            </label>
            <div className="space-y-2">
              <Input
                id="max-commits"
                type="number"
                min="1"
                value={settings.max_commits_deep_analysis}
                onChange={handleMaxCommitsChange}
                className={inputError ? 'border-red-500' : ''}
                placeholder="500"
              />
              {inputError && (
                <p className="text-sm text-red-600">{inputError}</p>
              )}
              <p className="text-sm text-gray-500">
                We'll analyze up to this many of your most recent commits and PRs with our AI system. 
                Default is 500, but you can set any number based on your needs. Higher numbers will take longer to process.
              </p>
            </div>
          </div>

          {/* Tech Debt Analysis Toggle */}
          <div>
            <div className="flex items-center space-x-3">
              <input
                id="tech-debt"
                type="checkbox"
                checked={settings.tech_debt_analysis_enabled}
                onChange={handleTechDebtToggle}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="tech-debt" className="text-sm font-medium text-gray-700">
                Enable Tech Debt Analysis
              </label>
            </div>
            <p className="text-sm text-gray-500 mt-1 ml-7">
              Automatically identify and track technical debt patterns in your codebase during analysis.
            </p>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-between pt-4">
            <Button
              variant="outline"
              onClick={onSkip}
              disabled={isLoading}
            >
              Use Defaults & Skip
            </Button>
            <Button
              onClick={handleSave}
              disabled={isLoading || !!inputError}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? 'Saving...' : 'Save Settings & Continue'}
            </Button>
          </div>
        </div>
      </Card>

      {/* Info Card */}
      <Card className="p-4 max-w-2xl mx-auto bg-blue-50 border-blue-200">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-600 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h3 className="text-sm font-medium text-blue-800 mb-1">
              How This Works
            </h3>
            <p className="text-sm text-blue-700">
              We'll start by loading all your PRs and commits, then perform deep LLM analysis on the most recent ones 
              (up to your specified limit). You can always adjust these settings later from the repository dashboard.
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
} 