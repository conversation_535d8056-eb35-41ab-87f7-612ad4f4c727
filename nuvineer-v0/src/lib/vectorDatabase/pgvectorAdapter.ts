/**
 * pgvector Database Adapter
 * 
 * This adapter implements the VectorDatabase interface for PostgreSQL with pgvector extension.
 * It provides vector storage and similarity search capabilities using PostgreSQL via Supabase.
 */

import {
  VectorDatabase,
  VectorSearchResult,
  VectorRecord,
  VectorQueryOptions,
  VectorUpsertRecord,
  VectorDatabaseStats,
  VectorDatabaseError,
  VectorUtils,
} from '../vectorDatabase';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import OpenAI from 'openai';

export class PgVectorDatabase extends VectorDatabase {
  private supabase: SupabaseClient;
  private openai: OpenAI;
  private tableName: string = 'decision_vectors';

  constructor(namespace: string) {
    super(namespace);
    
    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    const openaiApiKey = process.env.OPENAI_API_KEY;
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new VectorDatabaseError('Supabase configuration missing for pgvector', 'initialization');
    }

    if (!openaiApiKey) {
      throw new VectorDatabaseError('OpenAI API Key not configured for pgvector embedding generation', 'initialization');
    }
    
    this.supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        persistSession: false
      }
    });

    this.openai = new OpenAI({ apiKey: openaiApiKey });
    
    console.log(`[PgVectorAdapter] Initialized for namespace: ${namespace}`);
  }

  /**
   * Set the namespace context for RLS (Row Level Security)
   */
  private async setNamespaceContext(): Promise<void> {
    try {
      const { error } = await this.supabase.rpc('set_vector_namespace', {
        target_namespace: this.namespace
      });
      
      if (error) {
        throw new Error(`Failed to set namespace context: ${error.message}`);
      }
    } catch (error) {
      throw new VectorDatabaseError(
        `Failed to set namespace context for ${this.namespace}: ${error instanceof Error ? error.message : String(error)}`,
        'setNamespace',
        error instanceof Error ? error : undefined
      );
    }
  }

  async query(options: VectorQueryOptions): Promise<VectorSearchResult[]> {
    try {
      const {
        vector,
        topK,
        minScore = 0.7,
        filter = {},
        includeMetadata = true,
        includeValues = false,
      } = options;

      console.log(`[PgVectorAdapter] Using vectorSimilaritySearch function for namespace ${this.namespace}`);

      // Use the stored PostgreSQL function for more reliable vector search
      const data = await this.vectorSimilaritySearch(vector, topK, includeValues, includeMetadata, filter);

      // Convert to VectorSearchResult format
      const results: VectorSearchResult[] = (data || [])
        .filter((row: any) => {
          const score = 1 - row.distance; // Convert distance to similarity score
          return score >= minScore; // Filter by minimum score first
        })
        .map((row: any) => {
          const score = 1 - row.distance; // Convert distance to similarity score
          
          return {
            id: row.id,
            score,
            metadata: includeMetadata ? row.metadata : undefined,
            values: includeValues ? row.embedding : undefined,
          };
        });

      console.log(`[PgVectorAdapter] Query returned ${results.length} results for namespace ${this.namespace}`);
      return results;

    } catch (error) {
      console.error(`[PgVectorAdapter] Error querying namespace '${this.namespace}':`, error);
      throw new VectorDatabaseError(
        `Failed to query vectors: ${error instanceof Error ? error.message : String(error)}`,
        'query',
        error instanceof Error ? error : undefined
      );
    }
  }

  async upsert(vectors: VectorUpsertRecord[]): Promise<void> {
    try {
      // Set namespace context for RLS
      await this.setNamespaceContext();

      const vectorsToUpsert = [];
      
      for (const vector of vectors) {
        let embedding;
        
        // Check if vector has values (embedding)
        if (vector.values && vector.values.length > 0) {
          // Use provided embedding
          embedding = vector.values;
        } else if (vector.metadata) {
          // Generate embedding from metadata content
          const content = this.extractTextFromMetadata(vector.metadata);
          if (content) {
            embedding = await this.generateEmbedding(content);
          } else {
            console.warn(`[PgVectorAdapter] No content found for vector ${vector.id}, skipping`);
            continue;
          }
        } else {
          console.warn(`[PgVectorAdapter] Vector ${vector.id} has no values or metadata, skipping`);
          continue;
        }
        
        // Validate embedding dimensions
        if (!embedding || embedding.length === 0) {
          console.warn(`[PgVectorAdapter] Invalid embedding for vector ${vector.id}, skipping`);
          continue;
        }

        vectorsToUpsert.push({
          id: vector.id,
          namespace: this.namespace,
          embedding: embedding, // Pass as array, not string
          metadata: VectorUtils.sanitizeMetadata(vector.metadata),
          updated_at: new Date().toISOString(),
        });
      }

      if (vectorsToUpsert.length === 0) {
        console.warn(`[PgVectorAdapter] No valid vectors to upsert`);
        return;
      }

      console.log(`[PgVectorAdapter] Upserting ${vectorsToUpsert.length} vectors to namespace '${this.namespace}'`);

      const { error } = await this.supabase
        .from(this.tableName)
        .upsert(vectorsToUpsert, {
          onConflict: 'id,namespace'
        });

      if (error) {
        throw new Error(`Upsert failed: ${error.message}`);
      }

      console.log(`[PgVectorAdapter] Successfully upserted ${vectorsToUpsert.length} vectors`);

    } catch (error) {
      console.error(`[PgVectorAdapter] Error upserting vectors:`, error);
      throw new VectorDatabaseError(
        `Failed to upsert vectors: ${error instanceof Error ? error.message : String(error)}`,
        'upsert',
        error instanceof Error ? error : undefined
      );
    }
  }

  async fetch(ids: string[]): Promise<Record<string, VectorRecord>> {
    try {
      // Set namespace context for RLS
      await this.setNamespaceContext();

      console.log(`[PgVectorAdapter] Fetching ${ids.length} vectors from namespace '${this.namespace}'`);

      const { data, error } = await this.supabase
        .from(this.tableName)
        .select('id, embedding, metadata')
        .in('id', ids);

      if (error) {
        throw new Error(`Fetch failed: ${error.message}`);
      }

      const records: Record<string, VectorRecord> = {};

      for (const row of data || []) {
        records[row.id] = {
          id: row.id,
          values: row.embedding ? JSON.parse(row.embedding) : undefined,
          metadata: row.metadata || {},
        };
      }

      console.log(`[PgVectorAdapter] Fetched ${Object.keys(records).length} vectors`);
      return records;

    } catch (error) {
      console.error(`[PgVectorAdapter] Error fetching vectors:`, error);
      throw new VectorDatabaseError(
        `Failed to fetch vectors: ${error instanceof Error ? error.message : String(error)}`,
        'fetch',
        error instanceof Error ? error : undefined
      );
    }
  }

  async delete(ids: string[]): Promise<void> {
    try {
      // Set namespace context for RLS
      await this.setNamespaceContext();

      console.log(`[PgVectorAdapter] Deleting ${ids.length} vectors from namespace '${this.namespace}'`);

      const { error } = await this.supabase
        .from(this.tableName)
        .delete()
        .in('id', ids);

      if (error) {
        throw new Error(`Delete failed: ${error.message}`);
      }

      console.log(`[PgVectorAdapter] Successfully deleted ${ids.length} vectors`);

    } catch (error) {
      console.error(`[PgVectorAdapter] Error deleting vectors:`, error);
      throw new VectorDatabaseError(
        `Failed to delete vectors: ${error instanceof Error ? error.message : String(error)}`,
        'delete',
        error instanceof Error ? error : undefined
      );
    }
  }

  async getStats(): Promise<VectorDatabaseStats> {
    try {
      // Set namespace context for RLS
      await this.setNamespaceContext();

      const { data, error } = await this.supabase.rpc('get_namespace_vector_stats', {
        target_namespace: this.namespace
      });

      if (error) {
        throw new Error(`Stats query failed: ${error.message}`);
      }

      const stats = data?.[0] || {
        total_vectors: 0,
        active_vectors: 0,
        superseded_vectors: 0,
        created_today: 0
      };

      return {
        totalVectorCount: Number(stats.total_vectors),
        namespaces: {
          [this.namespace]: {
            vectorCount: Number(stats.active_vectors)
          }
        },
        dimension: 1536, // OpenAI text-embedding-3-small
      };

    } catch (error) {
      console.error(`[PgVectorAdapter] Error getting stats:`, error);
      throw new VectorDatabaseError(
        `Failed to get stats: ${error instanceof Error ? error.message : String(error)}`,
        'getStats',
        error instanceof Error ? error : undefined
      );
    }
  }

  async updateMetadata(id: string, metadata: Record<string, any>): Promise<void> {
    try {
      // Set namespace context for RLS
      await this.setNamespaceContext();

      const sanitizedMetadata = VectorUtils.sanitizeMetadata(metadata);

      console.log(`[PgVectorAdapter] Updating metadata for vector ${id} in namespace '${this.namespace}'`);

      const { error } = await this.supabase
        .from(this.tableName)
        .update({
          metadata: sanitizedMetadata,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id);

      if (error) {
        throw new Error(`Metadata update failed: ${error.message}`);
      }

      console.log(`[PgVectorAdapter] Successfully updated metadata for vector ${id}`);

    } catch (error) {
      console.error(`[PgVectorAdapter] Error updating metadata:`, error);
      throw new VectorDatabaseError(
        `Failed to update metadata: ${error instanceof Error ? error.message : String(error)}`,
        'updateMetadata',
        error instanceof Error ? error : undefined
      );
    }
  }

  async namespaceExists(): Promise<boolean> {
    try {
      // Set namespace context for RLS
      await this.setNamespaceContext();

      const { data, error } = await this.supabase
        .from(this.tableName)
        .select('id')
        .limit(1);

      if (error) {
        // If error is due to RLS policy (no access), namespace doesn't exist for this context
        return false;
      }

      // If we can query (even if no results), namespace exists
      return true;

    } catch (error) {
      console.warn(`[PgVectorAdapter] Error checking namespace existence:`, error);
      return false;
    }
  }

  /**
   * Generate embedding using OpenAI (uses shared utility for consistency)
   */
  async generateEmbedding(text: string, model = "text-embedding-3-small"): Promise<number[]> {
    try {
      const response = await this.openai.embeddings.create({
        model: model,
        input: text.replace(/\n/g, ' '),
        encoding_format: "float",
      });
      return response.data[0].embedding;
    } catch (error) {
      console.error('[PgVectorAdapter] Error generating OpenAI embedding:', error);
      throw new VectorDatabaseError(
        `Failed to generate embedding: ${error instanceof Error ? error.message : String(error)}`,
        'generateEmbedding',
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Helper method for semantic search with text query (maintains existing API)
   */
  async queryWithText(
    queryText: string,
    topK = 5,
    minScore = 0.7,
    filter?: Record<string, any>
  ): Promise<VectorSearchResult[]> {
    if (!queryText.trim()) {
      console.warn('[PgVectorAdapter] Empty queryText provided, returning empty results');
      return [];
    }

    const embedding = await this.generateEmbedding(queryText);
    return this.query({
      vector: embedding,
      topK,
      minScore,
      filter,
      includeMetadata: true,
      includeValues: false,
    });
  }

  /**
   * Apply metadata filters to the query
   */
  private applyMetadataFilter(query: any, filter: Record<string, any>): any {
    for (const [key, value] of Object.entries(filter)) {
      if (key === 'is_superseded') {
        // Handle boolean metadata fields
        query = query.eq(`metadata->>${key}`, String(value));
      } else if (key === 'related_files' && typeof value === 'object' && value.$in) {
        // Handle array contains queries
        for (const item of value.$in) {
          query = query.contains(`metadata->${key}`, JSON.stringify([item]));
        }
      } else if (key === 'domain_concepts' && typeof value === 'object' && value.$in) {
        // Handle array contains queries for domain concepts
        for (const concept of value.$in) {
          query = query.contains(`metadata->${key}`, JSON.stringify([concept]));
        }
      } else if (typeof value === 'string' || typeof value === 'number') {
        // Handle simple equality
        query = query.eq(`metadata->>${key}`, value);
      }
    }
    return query;
  }

  /**
   * Extract text content from metadata for embedding generation
   */
  private extractTextFromMetadata(metadata: Record<string, any>): string {
    const textFields = ['title', 'description', 'rationale', 'implications'];
    const textParts: string[] = [];
    
    for (const field of textFields) {
      if (metadata[field] && typeof metadata[field] === 'string') {
        textParts.push(metadata[field]);
      }
    }
    
    return textParts.join(' ').trim();
  }

  /**
   * Vector similarity search using stored PostgreSQL function
   * This provides a more reliable alternative to client-side SQL construction
   */
  async vectorSimilaritySearch(vector: number[], topK: number, includeValues = false, includeMetadata = false, filter = {}) {
    try {
      // Set namespace context for RLS
      await this.setNamespaceContext();

      const vectorString = `[${vector.join(',')}]`;
      
      console.log('[PgVector Debug] Using vectorSimilaritySearch function');
      console.log('[PgVector Debug] vectorString:', vectorString.substring(0, 100) + '...');
      console.log('[PgVector Debug] topK:', topK);
      console.log('[PgVector Debug] filter:', JSON.stringify(filter));
      
      const { data, error } = await this.supabase.rpc('vector_similarity_search', {
        query_vector: vectorString,
        similarity_threshold: 0,
        max_results: topK,
        table_filter: filter || {}
      });
      
      if (error) {
        console.error('[PgVector Debug] Vector search error:', error);
        throw error;
      }
      
      console.log('[PgVector Debug] Function returned', data?.length || 0, 'results');
      return data;
      
    } catch (error) {
      console.error('[PgVector Debug] Error in vectorSimilaritySearch:', error);
      throw new VectorDatabaseError(
        `Failed to execute vector similarity search: ${error instanceof Error ? error.message : String(error)}`,
        'vectorSimilaritySearch',
        error instanceof Error ? error : undefined
      );
    }
  }
} 