/**
 * Pinecone Vector Database Adapter
 * 
 * This adapter implements the VectorDatabase interface using Pinecone as the backend.
 * It maintains complete compatibility with the existing Pinecone implementation.
 */

import { Pinecone, Index } from '@pinecone-database/pinecone';
import OpenAI from 'openai';
import {
  VectorDatabase,
  VectorSearchResult,
  VectorRecord,
  VectorQueryOptions,
  VectorUpsertRecord,
  VectorDatabaseStats,
  VectorDatabaseError,
  VectorNotFoundError,
  NamespaceNotFoundError,
  VectorUtils,
} from '../vectorDatabase';

export class PineconeVectorDatabase extends VectorDatabase {
  private pinecone: Pinecone;
  private index: Index;
  private openai: OpenAI;
  private indexName: string;
  private static indexCache: Record<string, Index> = {};

  constructor(namespace: string) {
    super(namespace);

    const pineconeApiKey = process.env.PINECONE_API_KEY;
    const openaiApiKey = process.env.OPENAI_API_KEY;
    this.indexName = process.env.PINECONE_INDEX_NAME || 'architecture-decisions';

    if (!pineconeApiKey || !openaiApiKey) {
      throw new VectorDatabaseError(
        'Pinecone API Key or OpenAI API Key not configured',
        'initialization'
      );
    }

    this.pinecone = new Pinecone({ apiKey: pineconeApiKey });
    this.openai = new OpenAI({ apiKey: openaiApiKey });
    
    // Use cached index or create new one
    if (!PineconeVectorDatabase.indexCache[this.indexName]) {
      PineconeVectorDatabase.indexCache[this.indexName] = this.pinecone.Index(this.indexName);
    }
    this.index = PineconeVectorDatabase.indexCache[this.indexName];
  }

  async query(options: VectorQueryOptions): Promise<VectorSearchResult[]> {
    try {
      const {
        vector,
        topK,
        minScore = 0.7,
        filter,
        includeMetadata = true,
        includeValues = false,
      } = options;

      // Apply default filter for non-superseded decisions if no filter provided
      let finalFilter = filter;
      if (!filter) {
        finalFilter = { 'is_superseded': false };
      }

      const queryOptions: any = {
        vector,
        topK,
        includeMetadata,
        includeValues,
      };

      if (finalFilter) {
        queryOptions.filter = finalFilter;
      }

      console.log(`[PineconeAdapter] Querying namespace '${this.namespace}' (topK: ${topK}, minScore: ${minScore})`);
      
      const queryResponse = await this.index.namespace(this.namespace).query(queryOptions);
      const matches = queryResponse.matches || [];
      
      // Filter by minimum score and map to our interface
      const filteredMatches = matches
        .filter(match => match.score && match.score >= minScore)
        .map(match => ({
          id: match.id,
          score: match.score || 0,
          metadata: match.metadata || {},
        }));

      console.log(`[PineconeAdapter] Found ${matches.length} raw matches, ${filteredMatches.length} after score filtering`);
      return filteredMatches;

    } catch (error) {
      console.error(`[PineconeAdapter] Error querying namespace '${this.namespace}':`, error);
      throw new VectorDatabaseError(
        `Failed to query vectors: ${error instanceof Error ? error.message : String(error)}`,
        'query',
        error instanceof Error ? error : undefined
      );
    }
  }

  async upsert(vectors: VectorUpsertRecord[]): Promise<void> {
    try {
      const sanitizedVectors = vectors.map(vector => ({
        id: vector.id,
        values: vector.values,
        metadata: VectorUtils.sanitizeMetadata(vector.metadata),
      }));

      console.log(`[PineconeAdapter] Upserting ${vectors.length} vectors to namespace '${this.namespace}'`);
      await this.index.namespace(this.namespace).upsert(sanitizedVectors);
      console.log(`[PineconeAdapter] Successfully upserted ${vectors.length} vectors`);

    } catch (error) {
      console.error(`[PineconeAdapter] Error upserting vectors:`, error);
      throw new VectorDatabaseError(
        `Failed to upsert vectors: ${error instanceof Error ? error.message : String(error)}`,
        'upsert',
        error instanceof Error ? error : undefined
      );
    }
  }

  async fetch(ids: string[]): Promise<Record<string, VectorRecord>> {
    try {
      console.log(`[PineconeAdapter] Fetching ${ids.length} vectors from namespace '${this.namespace}'`);
      
      const fetchResponse = await this.index.namespace(this.namespace).fetch(ids);
      const records: Record<string, VectorRecord> = {};

      for (const [id, record] of Object.entries(fetchResponse.records || {})) {
        records[id] = {
          id,
          values: record.values,
          metadata: record.metadata || {},
        };
      }

      console.log(`[PineconeAdapter] Successfully fetched ${Object.keys(records).length} vectors`);
      return records;

    } catch (error) {
      console.error(`[PineconeAdapter] Error fetching vectors:`, error);
      throw new VectorDatabaseError(
        `Failed to fetch vectors: ${error instanceof Error ? error.message : String(error)}`,
        'fetch',
        error instanceof Error ? error : undefined
      );
    }
  }

  async delete(ids: string[]): Promise<void> {
    try {
      console.log(`[PineconeAdapter] Deleting ${ids.length} vectors from namespace '${this.namespace}'`);
      await this.index.namespace(this.namespace).deleteMany(ids);
      console.log(`[PineconeAdapter] Successfully deleted ${ids.length} vectors`);

    } catch (error) {
      console.error(`[PineconeAdapter] Error deleting vectors:`, error);
      throw new VectorDatabaseError(
        `Failed to delete vectors: ${error instanceof Error ? error.message : String(error)}`,
        'delete',
        error instanceof Error ? error : undefined
      );
    }
  }

  async getStats(): Promise<VectorDatabaseStats> {
    try {
      console.log(`[PineconeAdapter] Getting index stats`);
      const indexStats = await this.index.describeIndexStats();
      
      return {
        totalVectorCount: indexStats.totalRecordCount,
        dimension: indexStats.dimension,
        namespaces: indexStats.namespaces ? Object.fromEntries(
          Object.entries(indexStats.namespaces).map(([key, summary]) => [
            key, 
            { vectorCount: (summary as any).recordCount || 0 }
          ])
        ) : undefined,
      };

    } catch (error) {
      console.error(`[PineconeAdapter] Error getting stats:`, error);
      throw new VectorDatabaseError(
        `Failed to get database stats: ${error instanceof Error ? error.message : String(error)}`,
        'getStats',
        error instanceof Error ? error : undefined
      );
    }
  }

  async updateMetadata(id: string, metadata: Record<string, any>): Promise<void> {
    try {
      console.log(`[PineconeAdapter] Updating metadata for vector '${id}' in namespace '${this.namespace}'`);
      
      // Fetch the existing vector to preserve its embedding
      const fetchResponse = await this.index.namespace(this.namespace).fetch([id]);
      const existingRecord = fetchResponse.records?.[id];

      if (!existingRecord) {
        throw new VectorNotFoundError(id, this.namespace);
      }

      // Create upsert record with existing values and new metadata
      const vectorToUpdate: VectorUpsertRecord = {
        id,
        values: existingRecord.values || [],
        metadata: VectorUtils.sanitizeMetadata({
          ...existingRecord.metadata,
          ...metadata,
        }),
      };

      await this.upsert([vectorToUpdate]);
      console.log(`[PineconeAdapter] Successfully updated metadata for vector '${id}'`);

    } catch (error) {
      if (error instanceof VectorNotFoundError) {
        throw error;
      }
      console.error(`[PineconeAdapter] Error updating metadata:`, error);
      throw new VectorDatabaseError(
        `Failed to update metadata: ${error instanceof Error ? error.message : String(error)}`,
        'updateMetadata',
        error instanceof Error ? error : undefined
      );
    }
  }

  async namespaceExists(): Promise<boolean> {
    try {
      const stats = await this.getStats();
      return !!(stats.namespaces && stats.namespaces[this.namespace]);
    } catch (error) {
      console.warn(`[PineconeAdapter] Could not check namespace existence:`, error);
      return false;
    }
  }

  /**
   * Generate embedding using OpenAI (maintains compatibility with existing implementation)
   */
  async generateEmbedding(text: string, model = "text-embedding-3-small"): Promise<number[]> {
    try {
      const response = await this.openai.embeddings.create({
        model: model,
        input: text.replace(/\n/g, ' '),
        encoding_format: "float",
      });
      return response.data[0].embedding;
    } catch (error) {
      console.error('[PineconeAdapter] Error generating OpenAI embedding:', error);
      throw new VectorDatabaseError(
        `Failed to generate embedding: ${error instanceof Error ? error.message : String(error)}`,
        'generateEmbedding',
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Helper method for semantic search with text query (maintains existing API)
   */
  async queryWithText(
    queryText: string,
    topK = 5,
    minScore = 0.7,
    filter?: Record<string, any>
  ): Promise<VectorSearchResult[]> {
    if (!queryText.trim()) {
      console.warn('[PineconeAdapter] Empty queryText provided, returning empty results');
      return [];
    }

    const embedding = await this.generateEmbedding(queryText);
    return this.query({
      vector: embedding,
      topK,
      minScore,
      filter,
      includeMetadata: true,
      includeValues: false,
    });
  }
} 