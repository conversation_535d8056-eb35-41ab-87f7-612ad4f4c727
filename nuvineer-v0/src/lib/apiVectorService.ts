/**
 * API Vector Service
 * 
 * This service provides abstracted vector database operations for API routes.
 * It handles the conversion from namespace-based calls to installationId/repositorySlug calls.
 */

import { VectorDatabaseService } from './vectorDatabaseService';
import { getRepositoryNamespace } from './pinecone-utils';

export interface ApiVectorQueryOptions {
  topK?: number;
  filter?: Record<string, any>;
  includeMetadata?: boolean;
  includeValues?: boolean;
  minScore?: number;
}

export interface ApiVectorResult {
  id: string;
  score: number;
  metadata?: Record<string, any>;
  values?: number[];
}

/**
 * Extract installation ID and repository slug from namespace
 * This enables API routes to work with the abstracted layer
 */
function parseNamespace(namespace: string): { installationId: number; repositorySlug: string } {
  const parts = namespace.split('-');
  if (parts.length < 3) {
    throw new Error(`Invalid namespace format: ${namespace}`);
  }
  
  const installationId = parseInt(parts[0]);
  if (isNaN(installationId)) {
    throw new Error(`Invalid installation ID in namespace: ${parts[0]}`);
  }
  
  // Extract repository slug from namespace format: {installationId}-{owner}--{repo}
  const ownerRepoPart = parts.slice(1).join('-');
  const ownerRepoMatch = ownerRepoPart.match(/^(.+)--(.+)$/);
  
  if (!ownerRepoMatch) {
    throw new Error(`Invalid namespace format for repo extraction: ${namespace}`);
  }
  
  const repositorySlug = `${ownerRepoMatch[1]}/${ownerRepoMatch[2]}`;
  
  return { installationId, repositorySlug };
}

/**
 * API Vector Service Class
 */
export class ApiVectorService {
  
  /**
   * Query vectors using dummy vector approach (for fetching all vectors with filters)
   */
  static async queryAllVectors(
    namespace: string, 
    options: ApiVectorQueryOptions = {}
  ): Promise<ApiVectorResult[]> {
    const { installationId, repositorySlug } = parseNamespace(namespace);
    const { topK = 10000, filter = {}, includeMetadata = true, includeValues = false } = options;
    
    console.log(`[ApiVectorService] Querying all vectors for ${repositorySlug} (topK: ${topK})`);
    
    try {
      // Get the appropriate vector database for this repository
      const vectorDb = await VectorDatabaseService.getVectorDatabase(installationId, repositorySlug);
      
      // Use dummy vector for metadata-only queries
      const dummyVector = new Array(1536).fill(0);
      
      // Add default filter to exclude superseded decisions
      const baseFilter = { 'is_superseded': false };
      const finalFilter = filter && Object.keys(filter).length > 0 
        ? { $and: [baseFilter, filter] }
        : baseFilter;
      
      const results = await vectorDb.query({
        vector: dummyVector,
        topK,
        filter: finalFilter,
        includeMetadata,
        includeValues
      });
      
      console.log(`[ApiVectorService] Found ${results.length} vectors for ${repositorySlug}`);
      return results;
      
    } catch (error) {
      console.error(`[ApiVectorService] Error querying vectors for ${repositorySlug}:`, error);
      throw error;
    }
  }
  
  /**
   * Fetch specific vectors by IDs
   */
  static async fetchVectors(
    namespace: string, 
    ids: string[]
  ): Promise<Record<string, ApiVectorResult>> {
    const { installationId, repositorySlug } = parseNamespace(namespace);
    
    console.log(`[ApiVectorService] Fetching ${ids.length} vectors for ${repositorySlug}`);
    
    try {
      // Get the appropriate vector database for this repository
      const vectorDb = await VectorDatabaseService.getVectorDatabase(installationId, repositorySlug);
      
      const records = await vectorDb.fetch(ids);
      
      // Convert to API format
      const results: Record<string, ApiVectorResult> = {};
      for (const [id, record] of Object.entries(records)) {
        results[id] = {
          id: record.id,
          score: 1.0, // Default score for fetch operations
          metadata: record.metadata,
          values: record.values
        };
      }
      
      console.log(`[ApiVectorService] Fetched ${Object.keys(results).length} vectors for ${repositorySlug}`);
      return results;
      
    } catch (error) {
      console.error(`[ApiVectorService] Error fetching vectors for ${repositorySlug}:`, error);
      throw error;
    }
  }
  
  /**
   * Query vectors with semantic search
   */
  static async queryVectors(
    namespace: string,
    queryVector: number[],
    options: ApiVectorQueryOptions = {}
  ): Promise<ApiVectorResult[]> {
    const { installationId, repositorySlug } = parseNamespace(namespace);
    const { topK = 10, filter = {}, includeMetadata = true, includeValues = false, minScore = 0.7 } = options;
    
    console.log(`[ApiVectorService] Semantic query for ${repositorySlug} (topK: ${topK})`);
    
    try {
      // Get the appropriate vector database for this repository
      const vectorDb = await VectorDatabaseService.getVectorDatabase(installationId, repositorySlug);
      
      // Add default filter to exclude superseded decisions
      const baseFilter = { 'is_superseded': false };
      const finalFilter = filter && Object.keys(filter).length > 0 
        ? { $and: [baseFilter, filter] }
        : baseFilter;
      
      const results = await vectorDb.query({
        vector: queryVector,
        topK,
        filter: finalFilter,
        includeMetadata,
        includeValues
      });
      
      // Filter by minimum score
      const filteredResults = results.filter(result => result.score >= minScore);
      
      console.log(`[ApiVectorService] Found ${filteredResults.length} vectors above score ${minScore} for ${repositorySlug}`);
      return filteredResults;
      
    } catch (error) {
      console.error(`[ApiVectorService] Error in semantic query for ${repositorySlug}:`, error);
      throw error;
    }
  }
  
  /**
   * Get vector database statistics
   */
  static async getStats(namespace: string) {
    const { installationId, repositorySlug } = parseNamespace(namespace);
    
    console.log(`[ApiVectorService] Getting stats for ${repositorySlug}`);
    
    try {
      // Get the appropriate vector database for this repository
      const vectorDb = await VectorDatabaseService.getVectorDatabase(installationId, repositorySlug);
      
      const stats = await vectorDb.getStats();
      
      console.log(`[ApiVectorService] Retrieved stats for ${repositorySlug}:`, stats);
      return stats;
      
    } catch (error) {
      console.error(`[ApiVectorService] Error getting stats for ${repositorySlug}:`, error);
      throw error;
    }
  }
  
  /**
   * Check if a namespace exists
   */
  static async namespaceExists(namespace: string): Promise<boolean> {
    const { installationId, repositorySlug } = parseNamespace(namespace);
    
    try {
      // Get the appropriate vector database for this repository
      const vectorDb = await VectorDatabaseService.getVectorDatabase(installationId, repositorySlug);
      
      const exists = await vectorDb.namespaceExists();
      
      console.log(`[ApiVectorService] Namespace exists for ${repositorySlug}: ${exists}`);
      return exists;
      
    } catch (error) {
      console.error(`[ApiVectorService] Error checking namespace for ${repositorySlug}:`, error);
      return false;
    }
  }
  
  /**
   * Legacy compatibility: Get Pinecone-like index interface
   * This helps with minimal changes to existing API code
   */
  static async getLegacyIndex(namespace: string) {
    return {
      query: async (options: any) => {
        const results = await this.queryAllVectors(namespace, {
          topK: options.topK,
          filter: options.filter,
          includeMetadata: options.includeMetadata,
          includeValues: options.includeValues
        });
        
        // Return in Pinecone-compatible format
        return {
          matches: results
        };
      },
      
      fetch: async (ids: string[]) => {
        const results = await this.fetchVectors(namespace, ids);
        
        // Return in Pinecone-compatible format
        return {
          records: results
        };
      }
    };
  }
} 