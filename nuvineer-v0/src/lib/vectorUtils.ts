/**
 * Vector Database Utilities
 * 
 * This replaces the old pineconeUtils.ts with an abstracted version that works
 * with any vector database backend through the VectorDatabase interface.
 */

import { getVectorDatabaseForRepository } from './vectorDatabaseService';
import { VectorSearchResult } from './vectorDatabase';
import OpenAI from 'openai';

const openai = new OpenAI({ 
  apiKey: process.env.OPENAI_API_KEY! 
});

/**
 * Generate embedding using OpenAI (consistent with existing implementation)
 */
export async function generateEmbedding(text: string, model = "text-embedding-3-small"): Promise<number[]> {
  try {
    const response = await openai.embeddings.create({
      model: model,
      input: text.replace(/\n/g, ' '),
      encoding_format: "float",
    });
    return response.data[0].embedding;
  } catch (error) {
    console.error('[VectorUtils] Error generating OpenAI embedding:', error);
    throw error;
  }
}

/**
 * Query vector database for semantic search (replaces queryPinecone)
 * 
 * @param queryText Text to generate embedding for the query
 * @param installationId GitHub installation ID
 * @param repositorySlug Repository slug (owner/repo)
 * @param topK Number of results to return
 * @param minScore Minimum similarity score
 * @param filter Optional filter object for metadata filtering
 * @returns Array of matching results { id, score, metadata }
 */
export async function queryVectorDatabase(
  queryText: string,
  installationId: number | string,
  repositorySlug: string,
  topK = 5,
  minScore = 0.7,
  filter?: Record<string, any>
): Promise<VectorSearchResult[]> {
  if (!queryText.trim()) {
    console.warn('[VectorUtils] Empty queryText for vector query. Returning empty array.');
    return [];
  }

  console.log(`[VectorUtils] Querying vector database for repository '${repositorySlug}' with query "${queryText.substring(0,50)}..." (topK: ${topK}, minScore: ${minScore})`);

  try {
    const vectorDb = await getVectorDatabaseForRepository(installationId, repositorySlug);
    const queryEmbedding = await generateEmbedding(queryText);

    // Apply default filter for non-superseded decisions if no filter provided
    let finalFilter = filter;
    if (!filter) {
      finalFilter = { 'is_superseded': false };
      console.log(`[VectorUtils] Applying default filter: { 'is_superseded': false }`);
    } else {
      console.log(`[VectorUtils] Applying provided filter:`, filter);
    }

    const results = await vectorDb.query({
      vector: queryEmbedding,
      topK,
      minScore,
      filter: finalFilter,
      includeMetadata: true,
      includeValues: false,
    });

    console.log(`[VectorUtils] Found ${results.length} results after filtering for repository '${repositorySlug}'`);
    return results;

  } catch (error) {
    console.error(`[VectorUtils] Error querying vector database for repository '${repositorySlug}':`, error);
    return []; // Return empty on error to avoid breaking flows
  }
}

/**
 * Fetch a single vector by ID (replaces getVectorById)
 */
export async function getVectorById(
  id: string,
  installationId: number | string,
  repositorySlug: string
): Promise<any | null> {
  console.log(`[VectorUtils] Fetching vector ID '${id}' for repository '${repositorySlug}'`);
  
  try {
    const vectorDb = await getVectorDatabaseForRepository(installationId, repositorySlug);
    const records = await vectorDb.fetch([id]);
    
    return records[id] || null;
  } catch (error) {
    console.error(`[VectorUtils] Error fetching vector ID '${id}' for repository '${repositorySlug}':`, error);
    return null;
  }
}

/**
 * Fetch multiple vectors by IDs (replaces getVectorsByIds)
 */
export async function getVectorsByIds(
  ids: string[],
  installationId: number | string,
  repositorySlug: string
): Promise<any[]> {
  if (!ids || ids.length === 0) {
    return [];
  }

  console.log(`[VectorUtils] Fetching ${ids.length} vector IDs for repository '${repositorySlug}'. IDs: ${ids.join(', ')}`);
  
  try {
    const vectorDb = await getVectorDatabaseForRepository(installationId, repositorySlug);
    const records = await vectorDb.fetch(ids);
    
    // Convert records object into an array
    return ids.map(id => records[id]).filter(record => record !== undefined);
  } catch (error) {
    console.error(`[VectorUtils] Error fetching vectors by IDs for repository '${repositorySlug}':`, error);
    return [];
  }
}

/**
 * Store/upsert vectors to vector database
 */
export async function upsertVectors(
  vectors: Array<{
    id: string;
    values: number[];
    metadata: Record<string, any>;
  }>,
  installationId: number | string,
  repositorySlug: string
): Promise<void> {
  console.log(`[VectorUtils] Upserting ${vectors.length} vectors for repository '${repositorySlug}'`);
  
  try {
    const vectorDb = await getVectorDatabaseForRepository(installationId, repositorySlug);
    await vectorDb.upsert(vectors);
    console.log(`[VectorUtils] Successfully upserted ${vectors.length} vectors for repository '${repositorySlug}'`);
  } catch (error) {
    console.error(`[VectorUtils] Error upserting vectors for repository '${repositorySlug}':`, error);
    throw error;
  }
}

/**
 * Update vector metadata without changing the embedding
 */
export async function updateVectorMetadata(
  id: string,
  metadata: Record<string, any>,
  installationId: number | string,
  repositorySlug: string
): Promise<void> {
  console.log(`[VectorUtils] Updating metadata for vector '${id}' in repository '${repositorySlug}'`);
  
  try {
    const vectorDb = await getVectorDatabaseForRepository(installationId, repositorySlug);
    await vectorDb.updateMetadata(id, metadata);
    console.log(`[VectorUtils] Successfully updated metadata for vector '${id}' in repository '${repositorySlug}'`);
  } catch (error) {
    console.error(`[VectorUtils] Error updating metadata for vector '${id}' in repository '${repositorySlug}':`, error);
    throw error;
  }
}

/**
 * Get vector database statistics
 */
export async function getVectorDatabaseStats(
  installationId: number | string,
  repositorySlug: string
): Promise<any> {
  try {
    const vectorDb = await getVectorDatabaseForRepository(installationId, repositorySlug);
    return await vectorDb.getStats();
  } catch (error) {
    console.error(`[VectorUtils] Error getting vector database stats for repository '${repositorySlug}':`, error);
    throw error;
  }
}

// Legacy compatibility exports (for gradual migration)
export const queryPinecone = queryVectorDatabase; 