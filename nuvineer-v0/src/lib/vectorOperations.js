/**
 * Vector Database Operations (Simplified for Deployment)
 * 
 * This module provides abstracted vector database operations with strict namespace isolation.
 * Simplified JavaScript version for immediate deployment compatibility.
 */

import { VectorDatabaseService } from './vectorDatabaseService.ts';
import { getRepositoryNamespace } from './pinecone-utils.ts';

/**
 * Validates namespace format and ensures it matches the expected repository
 */
function validateNamespace(installationId, repositorySlug, providedNamespace) {
  const expectedNamespace = getRepositoryNamespace(installationId, repositorySlug);
  
  // If namespace was provided, validate it matches expected
  if (providedNamespace && providedNamespace !== expectedNamespace) {
    throw new Error(`Namespace mismatch: expected ${expectedNamespace}, got ${providedNamespace}`);
  }
  
  // Validate format
  if (!expectedNamespace.match(/^\d+-[a-z0-9-]+--[a-z0-9-]+$/)) {
    throw new Error(`Invalid namespace format: ${expectedNamespace}`);
  }
  
  return expectedNamespace;
}

/**
 * Extracts repository slug from namespace for validation
 */
function extractRepoSlugFromNamespace(namespace) {
  const parts = namespace.split('-');
  if (parts.length < 3) {
    throw new Error(`Cannot parse namespace: ${namespace}`);
  }
  
  const ownerRepoPart = parts.slice(1).join('-');
  const match = ownerRepoPart.match(/^(.+)--(.+)$/);
  
  if (!match) {
    throw new Error(`Invalid namespace format: ${namespace}`);
  }
  
  return `${match[1]}/${match[2]}`;
}

/**
 * Helper function to validate repository slug format consistency
 */
function validateRepositorySlug(metadataRepoSlug, expectedRepoSlug, logPrefix, itemId) {
  if (!metadataRepoSlug) {
    return true; // No metadata repository slug to validate
  }
  
  // Handle both formats: namespace format (66900160-jabubaker--jdeferred) and GitHub format (jabubaker/jdeferred)
  let normalizedMetadataSlug = metadataRepoSlug;
  
  // If it's in namespace format, extract the GitHub format
  if (metadataRepoSlug.includes('--')) {
    try {
      normalizedMetadataSlug = extractRepoSlugFromNamespace(metadataRepoSlug);
    } catch (error) {
      // If extraction fails, use the original value
      console.warn(`${logPrefix} Could not extract repo slug from namespace format: ${metadataRepoSlug}`);
    }
  }
  
  if (normalizedMetadataSlug !== expectedRepoSlug) {
    console.error(`${logPrefix} DATA CONTAMINATION DETECTED: ${itemId ? `Item ${itemId}` : 'Item'} belongs to ${normalizedMetadataSlug}, expected ${expectedRepoSlug}`);
    return false;
  }
  
  return true;
}

/**
 * Validates data isolation to prevent contamination
 */
function validateDataIsolation(namespace, metadata, operation) {
  const expectedRepoSlug = extractRepoSlugFromNamespace(namespace);
  
  if (metadata.repository_slug && metadata.repository_slug !== expectedRepoSlug) {
    throw new Error(`Data contamination detected in ${operation}: metadata.repository_slug (${metadata.repository_slug}) doesn't match namespace repository (${expectedRepoSlug})`);
  }
  
  // Ensure it's correctly set
  metadata.repository_slug = expectedRepoSlug;
}

/**
 * Store a decision record in the vector database
 */
export async function storeDecisionRecord(decisionRecord, installationId, repositorySlug) {
  const namespace = validateNamespace(installationId, repositorySlug);
  const logPrefix = `[VectorOps Store - Repo: ${repositorySlug}]`;
  
  console.log(`${logPrefix} Storing decision: "${decisionRecord.title}"`);

  try {
    // Get vector database instance for this repository
    const vectorDb = await VectorDatabaseService.getVectorDatabase(installationId, repositorySlug);

    // Generate unique ID
    const prNumber = decisionRecord.pr_number || 'unknownPR';
    const timestamp = Date.now();
    const contentToHash = `${decisionRecord.title || ''}-${decisionRecord.description || ''}`;
    const crypto = await import('crypto');
    const shortHash = crypto.createHash('sha256').update(contentToHash).digest('hex').substring(0, 8);
    const vectorId = `decision_${prNumber}_${timestamp}_${shortHash}`;

    // Prepare metadata with validation
    const metadata = {
      ...decisionRecord,
      repository_slug: repositorySlug,
      is_superseded: false
    };
    
    // Validate data isolation
    validateDataIsolation(namespace, metadata, 'storeDecisionRecord');

    // Store the vector
    const result = await vectorDb.upsert([{
      id: vectorId,
      metadata: metadata
    }]);

    console.log(`${logPrefix} Successfully stored decision with ID: ${vectorId}`);
    return vectorId;
    
  } catch (error) {
    console.error(`${logPrefix} Error storing decision:`, error);
    throw error;
  }
}

/**
 * Mark a decision as superseded
 */
export async function markDecisionAsSuperseded(supersededDecisionId, supersedingDecisionId, installationId, repositorySlug) {
  const namespace = validateNamespace(installationId, repositorySlug);
  const logPrefix = `[VectorOps Supersede - Repo: ${repositorySlug} - ID: ${supersededDecisionId}]`;

  console.log(`${logPrefix} Marking as superseded by ${supersedingDecisionId}`);

  try {
    // Get vector database instance for this repository
    const vectorDb = await VectorDatabaseService.getVectorDatabase(installationId, repositorySlug);

    // Update metadata to mark as superseded
    const updateResult = await vectorDb.updateMetadata(supersededDecisionId, {
      is_superseded: true,
      superseded_by_decision_id: supersedingDecisionId,
      superseded_at: new Date().toISOString()
    });

    console.log(`${logPrefix} Successfully marked as superseded`);
    return true;
    
  } catch (error) {
    console.error(`${logPrefix} Error marking as superseded:`, error);
    return false;
  }
}

/**
 * Generate embedding (simplified version)
 */
export async function generateEmbedding(text, model = "text-embedding-3-small") {
  try {
    const { default: OpenAI } = await import('openai');
    const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
    
    const response = await openai.embeddings.create({
      model: model,
      input: text.replace(/\n/g, ' '),
      encoding_format: "float",
    });
    
    return response.data[0].embedding;
  } catch (error) {
    console.error('[VectorOps] Error generating embedding:', error);
    throw error;
  }
}

/**
 * Query vector database
 */
export async function queryVectorDatabase(queryText, installationId, repositorySlug, options = {}) {
  const namespace = validateNamespace(installationId, repositorySlug);
  const { topK, k, minScore, filter = {} } = options;
  // Support both 'k' and 'topK' parameter names for backward compatibility
  const finalTopK = topK || k || 5;
  const finalMinScore = minScore !== undefined ? minScore : 0.7;
  const logPrefix = `[VectorOps Query - Repo: ${repositorySlug}]`;

  console.log(`${logPrefix} Query params: topK=${finalTopK}, minScore=${finalMinScore}`);

  try {
    // Get vector database instance
    const vectorDb = await VectorDatabaseService.getVectorDatabase(installationId, repositorySlug);

    // Generate query embedding
    const queryEmbedding = await generateEmbedding(queryText);

    // Execute query
    const results = await vectorDb.query({
      vector: queryEmbedding,
      topK: finalTopK,
      minScore: finalMinScore,
      filter: { ...filter, is_superseded: false },
      includeMetadata: true
    });

    // Validate results for data isolation (no need to re-filter by score since adapter already did it)
    const expectedRepoSlug = repositorySlug;
    const validatedResults = results.filter(result => {
      return validateRepositorySlug(result.metadata?.repository_slug, expectedRepoSlug, logPrefix, result.id);
    });

    console.log(`${logPrefix} Query returned ${validatedResults.length} validated results`);
    return validatedResults;
    
  } catch (error) {
    console.error(`${logPrefix} Error querying vector database:`, error);
    throw error;
  }
}

/**
 * Fetch vectors by IDs
 */
export async function fetchVectorsByIds(vectorIds, installationId, repositorySlug) {
  const namespace = validateNamespace(installationId, repositorySlug);
  const logPrefix = `[VectorOps Fetch - Repo: ${repositorySlug}]`;

  try {
    // Get vector database instance
    const vectorDb = await VectorDatabaseService.getVectorDatabase(installationId, repositorySlug);

    // Fetch vectors
    const records = await vectorDb.fetch(vectorIds);

    // Validate data isolation
    const expectedRepoSlug = repositorySlug;
    const validatedRecords = {};
    
    for (const [id, record] of Object.entries(records)) {
      if (validateRepositorySlug(record.metadata?.repository_slug, expectedRepoSlug, logPrefix, id)) {
        validatedRecords[id] = record;
      }
    }

    // Convert records object into an array (same format as original getVectorsByIds)
    const recordsArray = vectorIds.map(id => validatedRecords[id]).filter(record => record !== undefined);
    console.log(`${logPrefix} Fetched ${recordsArray.length} validated records as array`);
    return recordsArray;
    
  } catch (error) {
    console.error(`${logPrefix} Error fetching vectors:`, error);
    throw error;
  }
}

/**
 * Get vector database statistics
 */
export async function getVectorDatabaseStats(installationId, repositorySlug) {
  const logPrefix = `[VectorOps Stats - Repo: ${repositorySlug}]`;

  try {
    // Get vector database instance
    const vectorDb = await VectorDatabaseService.getVectorDatabase(installationId, repositorySlug);

    // Get stats
    const stats = await vectorDb.getStats();

    console.log(`${logPrefix} Retrieved database statistics`);
    return stats;
    
  } catch (error) {
    console.error(`${logPrefix} Error getting stats:`, error);
    throw error;
  }
}

/**
 * Query RAG for decision context (abstracted version)
 */
export async function queryRAG(decision, installationId, repositorySlug, k = 3, options = {}) {
  const namespace = validateNamespace(installationId, repositorySlug);
  const { minScore = 0.7, filter = null } = options;
  const queryText = `${decision.title}\n${decision.description}`;
  const logPrefix = `[VectorOps RAG - Repo: ${repositorySlug} - Decision: "${decision.title}"]`;

  console.log(`${logPrefix} Starting RAG query. Params: k=${k}, minScore=${minScore}`);

  try {
    // Get vector database instance
    const vectorDb = await VectorDatabaseService.getVectorDatabase(installationId, repositorySlug);

    // Generate embedding for the query text
    const queryEmbedding = await generateEmbedding(queryText);
    if (!queryEmbedding) {
      console.warn(`${logPrefix} Failed to generate query embedding. Returning empty results.`);
      return { contextString: "", fullMatches: [] };
    }

    // Prepare filter
    const baseFilter = { 'is_superseded': false };
    let combinedFilter = baseFilter;

    if (filter) {
      try {
        const userFilter = typeof filter === 'string' ? JSON.parse(filter) : filter;
        combinedFilter = { $and: [baseFilter, userFilter] };
        console.log(`${logPrefix} Combined user filter with base filter.`);
      } catch (e) {
        console.error(`${logPrefix} Invalid filter provided. Using base filter only.`, e);
      }
    }

    // Execute query
    const results = await vectorDb.query({
      vector: queryEmbedding,
      topK: k,
      filter: combinedFilter,
      includeMetadata: true
    });

    // Filter by score and validate data isolation
    const expectedRepoSlug = repositorySlug;
    const filteredMatches = results.filter(match => {
      // Data contamination check
      if (!validateRepositorySlug(match.metadata?.repository_slug, expectedRepoSlug, logPrefix, match.id)) {
        return false;
      }
      // Score threshold check
      return match.score >= minScore;
    });

    console.log(`${logPrefix} Query returned ${filteredMatches.length} validated matches (${results.length} raw matches)`);

    // Format context string (ID-only format for backward compatibility)
    const contextString = filteredMatches
      .map(match => `Previously seen Decision ID: ${match.id}`)
      .join('. ') + (filteredMatches.length > 0 ? '.' : '');

    return { 
      contextString: contextString, 
      fullMatches: filteredMatches 
    };

  } catch (error) {
    console.error(`${logPrefix} Error during RAG query:`, error);
    return { contextString: "", fullMatches: [] };
  }
}

/**
 * Find similar decisions (abstracted version)
 */
export async function findSimilarDecisions(decisionId, installationId, repositorySlug, k = 5, minScore = 0.7) {
  const namespace = validateNamespace(installationId, repositorySlug);
  const logPrefix = `[VectorOps Similar - Repo: ${repositorySlug} - ID: ${decisionId}]`;

  console.log(`${logPrefix} Finding top ${k} similar decisions (minScore: ${minScore})`);

  try {
    // Get vector database instance
    const vectorDb = await VectorDatabaseService.getVectorDatabase(installationId, repositorySlug);

    // Fetch the source decision
    const sourceRecords = await vectorDb.fetch([decisionId]);
    const sourceRecord = sourceRecords[decisionId];

    if (!sourceRecord) {
      throw new Error(`Source decision ${decisionId} not found`);
    }

    // If no vector values, regenerate embedding
    let sourceVector = sourceRecord.values;
    if (!sourceVector) {
      console.warn(`${logPrefix} Vector values not found for ${decisionId}. Regenerating embedding.`);
      const text = `${sourceRecord.metadata?.title || ''}\n${sourceRecord.metadata?.description || ''}`;
      if (!text.trim()) {
        throw new Error(`Cannot generate embedding for ${decisionId}: Missing title and description.`);
      }
      sourceVector = await generateEmbedding(text);
    }

    // Query for similar vectors
    const results = await vectorDb.query({
      vector: sourceVector,
      topK: k + 1, // +1 to potentially exclude self
      filter: { 'is_superseded': false },
      includeMetadata: true
    });

    // Filter results (exclude self and apply score threshold)
    const expectedRepoSlug = repositorySlug;
    const filteredMatches = results
      .filter(match => {
        // Exclude self
        if (match.id === decisionId) return false;
        
        // Data contamination check
        if (!validateRepositorySlug(match.metadata?.repository_slug, expectedRepoSlug, logPrefix, match.id)) {
          return false;
        }
        
        // Score threshold check
        return match.score >= minScore;
      })
      .slice(0, k); // Take top k after filtering

    console.log(`${logPrefix} Found ${filteredMatches.length} similar decisions`);
    return filteredMatches;

  } catch (error) {
    console.error(`${logPrefix} Error finding similar decisions:`, error);
    throw error;
  }
}

/**
 * Find decisions by file overlap (abstracted version)
 */
export async function findDecisionsByFileOverlap(newDecision, installationId, repositorySlug) {
  const namespace = validateNamespace(installationId, repositorySlug);
  const logPrefix = `[VectorOps FileOverlap - Repo: ${repositorySlug}]`;
  const relatedFiles = newDecision.related_files || [];

  if (relatedFiles.length === 0) {
    console.log(`${logPrefix} No related files found. Returning empty results.`);
    return [];
  }

  console.log(`${logPrefix} Searching for decisions related to files: ${relatedFiles.join(', ')}`);

  try {
    // Get vector database instance
    const vectorDb = await VectorDatabaseService.getVectorDatabase(installationId, repositorySlug);

    // Use a dummy vector query with metadata filter
    const dummyVector = new Array(1536).fill(0); // OpenAI embedding dimension
    
    const results = await vectorDb.query({
      vector: dummyVector,
      topK: 50,
      filter: {
        $and: [
          { 'is_superseded': false },
          { related_files: { $in: relatedFiles } }
        ]
      },
      includeMetadata: true
    });

    // Validate data isolation
    const expectedRepoSlug = repositorySlug;
    const validatedResults = results.filter(result => {
      return validateRepositorySlug(result.metadata?.repository_slug, expectedRepoSlug, logPrefix, result.id);
    });

    console.log(`${logPrefix} Found ${validatedResults.length} decisions with file overlap`);
    return validatedResults.map(result => ({ id: result.id, metadata: result.metadata }));

  } catch (error) {
    console.error(`${logPrefix} Error finding decisions by file overlap:`, error);
    return [];
  }
}

/**
 * Find decisions by domain concepts (abstracted version)
 */
export async function findDecisionsByDomainConcepts(newDecision, installationId, repositorySlug) {
  const namespace = validateNamespace(installationId, repositorySlug);
  const logPrefix = `[VectorOps DomainConcepts - Repo: ${repositorySlug}]`;
  const domainConcepts = newDecision.domain_concepts;

  if (!Array.isArray(domainConcepts) || domainConcepts.length === 0) {
    console.log(`${logPrefix} No valid domain concepts found. Returning empty results.`);
    return [];
  }

  console.log(`${logPrefix} Searching for decisions related to concepts: ${domainConcepts.join(', ')}`);

  try {
    // Get vector database instance
    const vectorDb = await VectorDatabaseService.getVectorDatabase(installationId, repositorySlug);

    // Use a dummy vector query with metadata filter
    const dummyVector = new Array(1536).fill(0); // OpenAI embedding dimension
    
    const results = await vectorDb.query({
      vector: dummyVector,
      topK: 50,
      filter: {
        $and: [
          { 'is_superseded': false },
          { domain_concepts: { $in: domainConcepts } }
        ]
      },
      includeMetadata: true
    });

    // Validate data isolation
    const expectedRepoSlug = repositorySlug;
    const validatedResults = results.filter(result => {
      return validateRepositorySlug(result.metadata?.repository_slug, expectedRepoSlug, logPrefix, result.id);
    });

    console.log(`${logPrefix} Found ${validatedResults.length} decisions with domain concept overlap`);
    return validatedResults.map(result => ({ id: result.id, metadata: result.metadata }));

  } catch (error) {
    console.error(`${logPrefix} Error finding decisions by domain concepts:`, error);
    return [];
  }
}

/**
 * Find decisions by impact overlap (abstracted version)
 */
export async function findDecisionsByImpactOverlap(newDecision, installationId, repositorySlug) {
  const namespace = validateNamespace(installationId, repositorySlug);
  const logPrefix = `[VectorOps ImpactOverlap - Repo: ${repositorySlug}]`;
  const implications = newDecision.implications || '';

  if (!implications.trim()) {
    console.log(`${logPrefix} No implications text found. Returning empty results.`);
    return [];
  }

  console.log(`${logPrefix} Searching for decisions with overlapping implications`);

  try {
    // Get vector database instance
    const vectorDb = await VectorDatabaseService.getVectorDatabase(installationId, repositorySlug);

    // Generate embedding for implications
    const implicationsEmbedding = await generateEmbedding(implications);
    
    const results = await vectorDb.query({
      vector: implicationsEmbedding,
      topK: 5,
      filter: { 'is_superseded': false },
      includeMetadata: true
    });

    // Filter by minimum score and validate data isolation
    const expectedRepoSlug = repositorySlug;
    const validatedResults = results.filter(result => {
      // Data contamination check
      if (!validateRepositorySlug(result.metadata?.repository_slug, expectedRepoSlug, logPrefix, result.id)) {
        return false;
      }
      // Score threshold check
      return result.score >= 0.4;
    });

    console.log(`${logPrefix} Found ${validatedResults.length} decisions with impact overlap`);
    return validatedResults.map(result => ({ 
      id: result.id, 
      metadata: result.metadata,
      score: result.score 
    }));

  } catch (error) {
    console.error(`${logPrefix} Error finding decisions by impact overlap:`, error);
    return [];
  }
}

/**
 * Test similarity between two decisions (abstracted version)
 */
export async function testDecisionSimilarity(decisionId1, decisionId2, installationId, repositorySlug) {
  const namespace = validateNamespace(installationId, repositorySlug);
  const logPrefix = `[VectorOps Similarity - Repo: ${repositorySlug}]`;
  
  console.log(`${logPrefix} Testing similarity between ${decisionId1} and ${decisionId2}`);

  try {
    // Get vector database instance
    const vectorDb = await VectorDatabaseService.getVectorDatabase(installationId, repositorySlug);

    // Fetch both decisions
    const records = await vectorDb.fetch([decisionId1, decisionId2]);
    
    if (!records[decisionId1] || !records[decisionId2]) {
      const missing = !records[decisionId1] ? decisionId1 : decisionId2;
      throw new Error(`Decision ${missing} not found`);
    }

    const decision1 = records[decisionId1];
    const decision2 = records[decisionId2];

    // Generate embeddings if missing
    let vector1 = decision1.values;
    let vector2 = decision2.values;

    if (!vector1) {
      const text1 = `${decision1.metadata?.title || ''}\n${decision1.metadata?.description || ''}`;
      if (text1.trim()) {
        vector1 = await generateEmbedding(text1);
      }
    }

    if (!vector2) {
      const text2 = `${decision2.metadata?.title || ''}\n${decision2.metadata?.description || ''}`;
      if (text2.trim()) {
        vector2 = await generateEmbedding(text2);
      }
    }

    if (!vector1 || !vector2) {
      throw new Error('Could not generate embeddings for similarity comparison');
    }

    // Calculate cosine similarity
    const dotProduct = vector1.reduce((sum, val, i) => sum + val * vector2[i], 0);
    const mag1 = Math.sqrt(vector1.reduce((sum, val) => sum + val * val, 0));
    const mag2 = Math.sqrt(vector2.reduce((sum, val) => sum + val * val, 0));
    
    const similarity = mag1 !== 0 && mag2 !== 0 ? dotProduct / (mag1 * mag2) : 0;

    const results = {
      similarity,
      similarityPercentage: (similarity * 100).toFixed(2) + '%',
      aboveThreshold: {
        high: similarity >= 0.8,
        medium: similarity >= 0.65 && similarity < 0.8,
        low: similarity < 0.65
      },
      decisions: {
        first: {
          id: decisionId1,
          title: decision1.metadata?.title || 'N/A',
          description: (decision1.metadata?.description || 'N/A').substring(0, 100) + '...'
        },
        second: {
          id: decisionId2,
          title: decision2.metadata?.title || 'N/A', 
          description: (decision2.metadata?.description || 'N/A').substring(0, 100) + '...'
        }
      }
    };

    console.log(`${logPrefix} Similarity: ${results.similarityPercentage}`);
    return results;

  } catch (error) {
    console.error(`${logPrefix} Error testing similarity:`, error);
    throw error;
  }
}

/**
 * Get all decision metadata for a repository (abstracted version)
 */
export async function getAllDecisionMetadataForRepo(installationId, repositorySlug, options = {}) {
  const namespace = validateNamespace(installationId, repositorySlug);
  const { includeSuperseded = false } = options;
  const logPrefix = `[VectorOps GetAll - Repo: ${repositorySlug}]`;
  
  console.log(`${logPrefix} Fetching all decisions. Include Superseded: ${includeSuperseded}`);

  try {
    // Get vector database instance
    const vectorDb = await VectorDatabaseService.getVectorDatabase(installationId, repositorySlug);

    // Use dummy vector to get all vectors
    const dummyVector = new Array(1536).fill(0);
    
    const results = await vectorDb.query({
      vector: dummyVector,
      topK: 10000,
      filter: includeSuperseded ? undefined : { 'is_superseded': false },
      includeMetadata: true
    });

    // Validate data isolation
    const expectedRepoSlug = repositorySlug;
    const validatedResults = results.filter(result => {
      return validateRepositorySlug(result.metadata?.repository_slug, expectedRepoSlug, logPrefix, result.id);
    });

    const decisions = validatedResults.map(result => ({
      id: result.id,
      metadata: result.metadata || {}
    }));

    console.log(`${logPrefix} Retrieved ${decisions.length} decisions`);
    return decisions;

  } catch (error) {
    console.error(`${logPrefix} Error fetching decision metadata:`, error);
    return [];
  }
} 