/**
 * Vector Database Service
 * 
 * This service manages vector database instances based on repository settings,
 * providing a unified interface that automatically selects the correct vector
 * database (Pinecone or pgvector) for each repository.
 */

import { VectorDatabase, VectorDatabaseFactory, VectorDatabaseType } from './vectorDatabase';
import { getRepositoryNamespace } from './pinecone-utils';
import { getRepositorySettings } from '../services/repositorySettingsService';

export class VectorDatabaseService {
  private static instanceCache: Map<string, VectorDatabase> = new Map();

  /**
   * Get vector database instance for a repository
   */
  static async getVectorDatabase(
    installationId: number | string,
    repositorySlug: string
  ): Promise<VectorDatabase> {
    const namespace = getRepositoryNamespace(installationId, repositorySlug);
    const cacheKey = `${namespace}`;

    // Check cache first
    if (this.instanceCache.has(cacheKey)) {
      return this.instanceCache.get(cacheKey)!;
    }

    // Determine vector database type from repository settings
    const vectorDbType = await this.getVectorDatabaseType(repositorySlug);
    
    console.log(`[VectorDatabaseService] Creating ${vectorDbType} instance for repository ${repositorySlug} (namespace: ${namespace})`);

    // Create new instance
    const vectorDb = await VectorDatabaseFactory.create({
      type: vectorDbType,
      namespace,
    });

    // Cache the instance
    this.instanceCache.set(cacheKey, vectorDb);
    return vectorDb;
  }

  /**
   * Get vector database type from repository settings
   */
  private static async getVectorDatabaseType(repositorySlug: string): Promise<VectorDatabaseType> {
    try {
      const { data: settings } = await getRepositorySettings(repositorySlug);
      
      if (settings?.vector_db) {
        // Clean up any PostgreSQL type casting that might be included in the value
        const cleanVectorDb = String(settings.vector_db).replace(/::text$/, '').trim();
        
        // Validate it's a supported type
        if (cleanVectorDb === 'pinecone' || cleanVectorDb === 'pgvector') {
          return cleanVectorDb as VectorDatabaseType;
        }
        
        console.warn(`[VectorDatabaseService] Invalid vector_db value '${settings.vector_db}' for ${repositorySlug}, defaulting to pgvector`);
      }
      
      // Default to pgvector for repositories without explicit settings
      console.log(`[VectorDatabaseService] No vector_db setting found for ${repositorySlug}, defaulting to pgvector`);
      return 'pgvector';
      
    } catch (error) {
      console.warn(`[VectorDatabaseService] Error fetching repository settings for ${repositorySlug}, defaulting to pgvector:`, error);
      return 'pgvector';
    }
  }

  /**
   * Clear cache for a specific repository (useful for testing or settings changes)
   */
  static clearCache(installationId: number | string, repositorySlug: string): void {
    const namespace = getRepositoryNamespace(installationId, repositorySlug);
    const cacheKey = `${namespace}`;
    this.instanceCache.delete(cacheKey);
  }

  /**
   * Clear all cached instances
   */
  static clearAllCache(): void {
    this.instanceCache.clear();
  }

  /**
   * Get vector database directly by type (for admin operations)
   */
  static async getVectorDatabaseByType(
    type: VectorDatabaseType,
    installationId: number | string,
    repositorySlug: string
  ): Promise<VectorDatabase> {
    const namespace = getRepositoryNamespace(installationId, repositorySlug);
    
    return VectorDatabaseFactory.create({
      type,
      namespace,
    });
  }
}

/**
 * Convenience functions that maintain compatibility with existing code
 */

/**
 * Get vector database for a repository (main entry point)
 */
export async function getVectorDatabaseForRepository(
  installationId: number | string,
  repositorySlug: string
): Promise<VectorDatabase> {
  return VectorDatabaseService.getVectorDatabase(installationId, repositorySlug);
}

/**
 * Legacy compatibility function - creates Pinecone instance directly
 * This can be used during migration to ensure existing code works
 */
export async function getPineconeVectorDatabase(
  installationId: number | string,
  repositorySlug: string
): Promise<VectorDatabase> {
  return VectorDatabaseService.getVectorDatabaseByType('pinecone', installationId, repositorySlug);
} 