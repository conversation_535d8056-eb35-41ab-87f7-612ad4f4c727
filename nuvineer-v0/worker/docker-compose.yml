services:
  nuvineer-worker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nuvineer-worker
    restart: unless-stopped
    ports:
      - '8080:8080'
    environment:
      - NODE_ENV=production
      - PORT=8080
      # Supabase configuration
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      # CORS configuration
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS:-*}
      # Cron job security
      - CRON_SECRET=${CRON_SECRET}
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs

    networks:
      - nuvineer-network

networks:
  nuvineer-network:
    driver: bridge
