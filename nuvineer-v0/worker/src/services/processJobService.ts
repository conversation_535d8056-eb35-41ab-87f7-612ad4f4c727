import {
  supabaseAdmin,
  getOctokit,
  getRepositorySettings,
  processMergedPR,
  getAllDecisionMetadataForRepo,
  getRepositoryNamespace,
  logger,
} from '../utils';
import { PendingPR } from '../types';

const MAX_PROCESSING_RETRIES = 3;

export async function getNextPendingPRs(
  batchSize: number = 5
): Promise<PendingPR[]> {
  try {
    const { data: pendingPRs, error } = await supabaseAdmin
      .from('repository_pr_analysis_status')
      .select('*')
      .eq('status', 'pending')
      .order('pr_merged_at', { ascending: false })
      .limit(batchSize);

    if (error) {
      logger.error('[Process Job Service] Error fetching pending PRs:', error);
      return [];
    }

    return pendingPRs || [];
  } catch (error) {
    logger.error('[Process Job Service] Error in getNextPendingPRs:', error);
    return [];
  }
}

export async function updateProcessingStatus(
  repositorySlug: string,
  prNumber: number,
  installationId: number,
  status: string,
  error?: string
): Promise<boolean> {
  try {
    const updateData: any = {
      status,
      last_analyzed_at: new Date().toISOString(),
    };

    if (error) {
      updateData.analysis_error = error;
    }

    const { error: updateError } = await supabaseAdmin
      .from('repository_pr_analysis_status')
      .update(updateData)
      .eq('repository_slug', repositorySlug)
      .eq('pr_number', prNumber)
      .eq('installation_id', installationId);

    if (updateError) {
      console.error(
        `[Process Job Service] Error updating status for PR #${prNumber}:`,
        updateError
      );
      return false;
    }

    return true;
  } catch (error) {
    console.error(
      `[Process Job Service] Error in updateProcessingStatus for PR #${prNumber}:`,
      error
    );
    return false;
  }
}

export async function incrementRetryCount(
  repositorySlug: string,
  prNumber: number,
  installationId: number
): Promise<number> {
  try {
    const { data: currentRecord, error: fetchError } = await supabaseAdmin
      .from('repository_pr_analysis_status')
      .select('processing_retries')
      .eq('repository_slug', repositorySlug)
      .eq('pr_number', prNumber)
      .eq('installation_id', installationId)
      .single();

    if (fetchError) {
      logger.error(
        `[Process Job Service] Error fetching retry count for PR #${prNumber}:`,
        fetchError
      );
      return 0;
    }

    const newRetryCount = (currentRecord?.processing_retries || 0) + 1;

    const { error: updateError } = await supabaseAdmin
      .from('repository_pr_analysis_status')
      .update({ processing_retries: newRetryCount })
      .eq('repository_slug', repositorySlug)
      .eq('pr_number', prNumber)
      .eq('installation_id', installationId);

    if (updateError) {
      logger.error(
        `[Process Job Service] Error updating retry count for PR #${prNumber}:`,
        updateError
      );
      return currentRecord?.processing_retries || 0;
    }

    return newRetryCount;
  } catch (error) {
    logger.error(
      `[Process Job Service] Error in incrementRetryCount for PR #${prNumber}:`,
      error
    );
    return 0;
  }
}

export async function processPendingPR(
  pr: PendingPR
): Promise<{ success: boolean; status: string; error?: string }> {
  const { repository_slug, pr_number, installation_id, is_pseudo_pr } = pr;

  console.log(
    `[Process Job Service] Processing ${
      is_pseudo_pr ? 'commit' : 'PR'
    } #${pr_number} from ${repository_slug}`
  );

  try {
    // Mark as processing
    await updateProcessingStatus(
      repository_slug,
      Number(pr_number),
      installation_id,
      'processing'
    );

    // Get repository settings
    const { data: settings, error: settingsError } =
      await getRepositorySettings(repository_slug);
    if (settingsError) {
      logger.error(
        `[Process Job Service] Error fetching repository settings for ${repository_slug}:`,
        settingsError
      );
      return {
        success: false,
        status: 'failed',
        error: 'Could not fetch repository settings',
      };
    }

    // Determine effective installation ID
    const effectiveInstallationId =
      installation_id === 0 ? undefined : installation_id.toString();

    // Get GitHub client and split repository
    const octokit = await getOctokit(effectiveInstallationId);
    const [owner, repo] = repository_slug.split('/');
    const pineconeNamespace = getRepositoryNamespace(
      effectiveInstallationId || '0',
      repository_slug
    );

    // Check for existing decisions in Pinecone first
    try {
      const allDecisions = await getAllDecisionMetadataForRepo(
        installation_id,
        repository_slug,
        { includeSuperseded: true }
      );
      let existingDecisions = [];

      if (is_pseudo_pr && pr.commit_sha) {
        existingDecisions = allDecisions.filter(
          (d: any) => d.metadata?.commit_sha === pr.commit_sha
        );
      } else {
        const currentPrNumberStr = String(pr_number);
        existingDecisions = allDecisions.filter(
          (d: any) => String(d.metadata?.pr_number) === currentPrNumberStr
        );
      }

      if (existingDecisions.length > 0) {
        console.log(
          `[Process Job Service] Found ${
            existingDecisions.length
          } existing decisions for ${
            is_pseudo_pr ? 'commit' : 'PR'
          } #${pr_number}. Skipping analysis.`
        );

        await updateProcessingStatus(
          repository_slug,
          Number(pr_number),
          installation_id,
          'completed',
          'Skipped: Analysis already performed'
        );
        return { success: true, status: 'completed' };
      }
    } catch (pineconeError) {
      logger.error(
        `[Process Job Service] Error checking existing decisions for ${
          is_pseudo_pr ? 'commit' : 'PR'
        } #${pr_number}:`,
        pineconeError
      );
    }

    // Get code changes
    let codeChanges: any[] = [];
    if (is_pseudo_pr && pr.commit_sha) {
      // Fetch code changes for commit
      const { data: commitDetail } = await octokit.repos.getCommit({
        owner,
        repo,
        ref: pr.commit_sha,
      });
      codeChanges = (commitDetail.files || []).map((file: any) => ({
        filename: file.filename,
        additions: file.additions,
        deletions: file.deletions,
        patch: file.patch || '',
        sha: file.sha,
        status: file.status,
      }));
    } else {
      // Fetch code changes for PR
      const numericPrNumber = Number(pr_number);
      const { data: files } = await octokit.pulls.listFiles({
        owner,
        repo,
        pull_number: numericPrNumber,
      });

      codeChanges = (files || []).map((file: any) => ({
        filename: file.filename,
        additions: file.additions,
        deletions: file.deletions,
        patch: file.patch || '',
        sha: file.sha,
        status: file.status,
      }));
    }

    // Get PR comments
    let prComments: any[] = [];
    if (!is_pseudo_pr) {
      const numericPrNumber = Number(pr_number);
      const { data: reviewComments } = await octokit.pulls.listReviewComments({
        owner,
        repo,
        pull_number: numericPrNumber,
      });

      prComments = reviewComments.map((c: any) => ({
        body: c.body,
        user: c.user,
        created_at: c.created_at,
      }));
    }

    // Get PR data if not a commit
    let ghPrData;
    if (!is_pseudo_pr) {
      const numericPrNumber = Number(pr_number);
      const { data } = await octokit.pulls.get({
        owner,
        repo,
        pull_number: numericPrNumber,
      });

      ghPrData = data;
    }

    // Prepare PR context for orchestrator
    const prContextAPI: any = {
      title: pr.pr_title,
      body: is_pseudo_pr ? '' : ghPrData?.body || '',
      html_url: pr.pr_url,
      number: pr_number,
      merged_at: pr.pr_merged_at,
      user: is_pseudo_pr
        ? pr.pr_title.includes('@')
          ? pr.pr_title.split('@')[1]
          : 'unknown'
        : ghPrData?.user?.login || 'unknown',
      is_pseudo_pr: is_pseudo_pr,
      commit_sha: is_pseudo_pr ? pr.commit_sha : undefined,
    };

    // Process PR with orchestrator
    const result = await processMergedPR(
      prContextAPI,
      codeChanges,
      prComments,
      pineconeNamespace,
      repository_slug,
      undefined // No design doc
    );

    // Update PR status based on result
    let finalStatus = 'failed';
    let errorMessage = null;
    let decisionsExtracted = 0;

    if (
      result.status === 'completed_successfully' ||
      result.status === 'completed_with_errors'
    ) {
      finalStatus = 'completed';
      decisionsExtracted = result.decisions?.length || 0;
    } else if (result.status === 'completed_no_decisions') {
      finalStatus = 'no_decisions';
      errorMessage = result.reason || null;
    } else {
      errorMessage = result.error || result.reason || 'Analysis failed';
    }

    // Update database with final status
    await updateProcessingStatus(
      repository_slug,
      Number(pr_number),
      installation_id,
      finalStatus,
      errorMessage || undefined
    );

    console.log(
      `[Process Job Service] Successfully processed ${
        is_pseudo_pr ? 'commit' : 'PR'
      } #${pr_number} with status: ${finalStatus}`
    );
    return { success: true, status: finalStatus };
  } catch (error) {
    console.error(
      `[Process Job Service] Error processing ${
        is_pseudo_pr ? 'commit' : 'PR'
      } #${pr_number}:`,
      error
    );

    const errorMessage = error instanceof Error ? error.message : String(error);

    // Check if we should retry or mark as failed
    const retryCount = await incrementRetryCount(
      repository_slug,
      Number(pr_number),
      installation_id
    );

    if (retryCount >= MAX_PROCESSING_RETRIES) {
      await updateProcessingStatus(
        repository_slug,
        Number(pr_number),
        installation_id,
        'failed',
        `Max retries exceeded: ${errorMessage}`
      );
      return {
        success: false,
        status: 'failed',
        error: `Max retries exceeded: ${errorMessage}`,
      };
    } else {
      await updateProcessingStatus(
        repository_slug,
        Number(pr_number),
        installation_id,
        'pending',
        `Retry ${retryCount}: ${errorMessage}`
      );
      return {
        success: false,
        status: 'pending',
        error: `Retry ${retryCount}: ${errorMessage}`,
      };
    }
  }
}

export async function processBatch(pendingPRs: PendingPR[]): Promise<{
  processed: number;
  successful: number;
  failed: number;
  retried: number;
}> {
  console.log(
    `[Process Job Service] Processing batch of ${pendingPRs.length} PRs...`
  );

  const results = await Promise.all(
    pendingPRs.map(async pr => {
      return await processPendingPR(pr);
    })
  );

  let successful = 0;
  let failed = 0;
  let retried = 0;

  for (const result of results) {
    if (result.success) {
      successful++;
    } else if (result.status === 'failed') {
      failed++;
    } else if (result.status === 'pending') {
      retried++;
    }
  }

  console.log(
    `[Process Job Service] Batch complete: ${successful} successful, ${failed} failed, ${retried} retried`
  );

  return {
    processed: pendingPRs.length,
    successful,
    failed,
    retried,
  };
}

export async function cleanupStuckProcessingItems(): Promise<{
  cleaned: number;
}> {
  try {
    // Find items that have been in 'processing' status for more than 30 minutes
    const thirtyMinutesAgo = new Date(
      Date.now() - 30 * 60 * 1000
    ).toISOString();

    const { data: stuckItems, error: fetchError } = await supabaseAdmin
      .from('repository_pr_analysis_status')
      .select('repository_slug, pr_number, installation_id')
      .eq('status', 'processing')
      .lt('last_analyzed_at', thirtyMinutesAgo);

    if (fetchError) {
      logger.error(
        '[Process Job Service] Error fetching stuck processing items:',
        fetchError
      );
      return { cleaned: 0 };
    }

    if (!stuckItems || stuckItems.length === 0) {
      return { cleaned: 0 };
    }

    logger.info(
      `[Process Job Service] Found ${stuckItems.length} stuck processing items, reverting to pending`
    );

    // Update all stuck items back to pending
    const { error: updateError } = await supabaseAdmin
      .from('repository_pr_analysis_status')
      .update({
        status: 'pending',
        last_analyzed_at: new Date().toISOString(),
        analysis_error: 'Reverted from stuck processing state',
      })
      .eq('status', 'processing')
      .lt('last_analyzed_at', thirtyMinutesAgo);

    if (updateError) {
      logger.error(
        '[Process Job Service] Error cleaning up stuck processing items:',
        updateError
      );
      return { cleaned: 0 };
    }

    console.log(
      `[Process Job Service] Successfully cleaned up ${stuckItems.length} stuck processing items`
    );
    return { cleaned: stuckItems.length };
  } catch (error) {
    logger.error(
      '[Process Job Service] Error in cleanupStuckProcessingItems:',
      error
    );
    return { cleaned: 0 };
  }
}
