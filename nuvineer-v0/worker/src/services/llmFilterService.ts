import {
  supabaseAdmin,
  getOctokit,
  getRepositorySettings,
  callLightweightLlmForSignificance,
  logger,
} from '../utils';
import { PendingEarlyStageItem } from '../types';

/**
 * Check if we can promote more items to pending status based on analysis limits
 */
export async function canPromoteMoreItems(
  repositorySlug: string,
  installationId: number
): Promise<{
  canPromote: boolean;
  maxLimit: number;
  currentPendingCount: number;
  deepAnalyzedCount: number;
}> {
  try {
    const { data: settings, error: settingsError } =
      await getRepositorySettings(repositorySlug);
    if (settingsError) {
      logger.error(
        `[LLM Filter Service] Error fetching repository settings for ${repositorySlug}:`,
        settingsError
      );
      return {
        canPromote: true,
        maxLimit: 500,
        currentPendingCount: 0,
        deepAnalyzedCount: 0,
      };
    }

    const maxAnalysis = settings?.max_commits_deep_analysis || 500;

    const { count: deepAnalyzedCount, error: deepCountError } =
      await supabaseAdmin
        .from('repository_pr_analysis_status')
        .select('*', { count: 'exact', head: true })
        .eq('repository_slug', repositorySlug)
        .eq('installation_id', installationId)
        .in('status', ['completed', 'no_decisions', 'failed']);

    if (deepCountError) {
      logger.error(
        `[LLM Filter Service] Error counting deep analyzed items:`,
        deepCountError
      );
      return {
        canPromote: true,
        maxLimit: maxAnalysis,
        currentPendingCount: 0,
        deepAnalyzedCount: 0,
      };
    }

    const { count: pendingCount, error: pendingCountError } =
      await supabaseAdmin
        .from('repository_pr_analysis_status')
        .select('*', { count: 'exact', head: true })
        .eq('repository_slug', repositorySlug)
        .eq('installation_id', installationId)
        .eq('status', 'pending');

    if (pendingCountError) {
      logger.error(
        `[LLM Filter Service] Error counting pending items:`,
        pendingCountError
      );
      return {
        canPromote: true,
        maxLimit: maxAnalysis,
        currentPendingCount: 0,
        deepAnalyzedCount: deepAnalyzedCount || 0,
      };
    }

    const totalInAnalysis = (deepAnalyzedCount || 0) + (pendingCount || 0);
    const canPromote = totalInAnalysis < maxAnalysis;

    logger.info(
      `[LLM Filter Service] Analysis limit check for ${repositorySlug}: ${totalInAnalysis}/${maxAnalysis} (${
        deepAnalyzedCount || 0
      } deep analyzed, ${
        pendingCount || 0
      } pending) - can promote: ${canPromote}`
    );

    return {
      canPromote,
      maxLimit: maxAnalysis,
      currentPendingCount: pendingCount || 0,
      deepAnalyzedCount: deepAnalyzedCount || 0,
    };
  } catch (error) {
    logger.error(
      `[LLM Filter Service] Error checking promotion limits:`,
      error
    );
    return {
      canPromote: true,
      maxLimit: 500,
      currentPendingCount: 0,
      deepAnalyzedCount: 0,
    };
  }
}

export async function getNextPendingEarlyStageItems(
  batchSize: number = 10
): Promise<PendingEarlyStageItem[]> {
  try {
    const { data: items, error } = await supabaseAdmin
      .from('repository_pr_analysis_status')
      .select('*')
      .eq('status', 'pending_early_stage')
      .order('pr_merged_at', { ascending: false })
      .limit(batchSize);

    if (error) {
      logger.error(
        '[LLM Filter Service] Error fetching pending_early_stage items:',
        error
      );
      return [];
    }

    return items || [];
  } catch (error) {
    logger.error(
      '[LLM Filter Service] Error in getNextPendingEarlyStageItems:',
      error
    );
    return [];
  }
}

export function extractFileContext(files: any[]) {
  const fileNames = files.map((file: any) => file.filename);
  const directories = [
    ...new Set(
      fileNames.map((filename: string) => {
        const parts = filename.split('/');
        return parts.length > 1 ? parts.slice(0, -1).join('/') : '.';
      })
    ),
  ].filter(dir => dir !== '.');

  const fileTypes = [
    ...new Set(
      fileNames.map((filename: string) => {
        const ext = filename.split('.').pop();
        return ext ? `.${ext}` : 'no-extension';
      })
    ),
  ];

  const totalAdditions = files.reduce(
    (sum: number, file: any) => sum + (file.additions || 0),
    0
  );
  const totalDeletions = files.reduce(
    (sum: number, file: any) => sum + (file.deletions || 0),
    0
  );

  return {
    filesChanged: files.length,
    totalAdditions,
    totalDeletions,
    directories,
    fileNames,
    fileTypes,
  };
}

export async function processItemWithLlm(
  item: PendingEarlyStageItem
): Promise<{ success: boolean; newStatus: string; error?: string }> {
  try {
    logger.info(
      `[LLM Filter Service] Processing ${
        item.is_pseudo_pr ? 'commit' : 'PR'
      } #${item.pr_number} from ${item.repository_slug}`
    );

    let itemDetails = `Title: ${item.pr_title}\n\nDescription: No description provided.`;
    let fileContext = undefined;

    if (item.is_pseudo_pr && item.commit_sha) {
      itemDetails = `Commit: ${item.commit_sha.substring(0, 7)}\nTitle: ${
        item.pr_title
      }\n\nDescription: Direct commit to repository.`;

      try {
        const [owner, repo] = item.repository_slug.split('/');
        const effectiveInstallationId =
          item.installation_id === 0
            ? undefined
            : item.installation_id.toString();
        const octokit = await getOctokit(effectiveInstallationId);

        const commitData = await octokit.rest.repos.getCommit({
          owner,
          repo,
          ref: item.commit_sha,
        });

        if (commitData.data.files) {
          fileContext = extractFileContext(commitData.data.files);
        }
      } catch (fileError) {
        logger.warn(
          `[LLM Filter Service] Could not fetch file context for commit ${item.commit_sha}:`,
          fileError
        );
      }
    } else {
      try {
        const [owner, repo] = item.repository_slug.split('/');
        const effectiveInstallationId =
          item.installation_id === 0
            ? undefined
            : item.installation_id.toString();
        const octokit = await getOctokit(effectiveInstallationId);

        const prFiles = await octokit.rest.pulls.listFiles({
          owner,
          repo,
          pull_number: Number(item.pr_number),
          per_page: 100,
        });

        if (prFiles.data) {
          fileContext = extractFileContext(prFiles.data);
        }
      } catch (fileError) {
        logger.warn(
          `[LLM Filter Service] Could not fetch file context for PR #${item.pr_number}:`,
          fileError
        );
      }
    }

    const significanceResult = await callLightweightLlmForSignificance(
      itemDetails,
      fileContext
    );
    const isSignificant = significanceResult.sig === 1;

    let newStatus = 'skipped_not_significant';

    if (isSignificant) {
      const { canPromote } = await canPromoteMoreItems(
        item.repository_slug,
        item.installation_id
      );

      if (canPromote) {
        newStatus = 'pending';
      } else {
        logger.warn(
          `[LLM Filter Service] Item #${item.pr_number} is significant but analysis limit reached - marking as analysis_limit_reached`
        );
        newStatus = 'analysis_limit_reached';
      }
    }

    const contextSummary = fileContext
      ? `${fileContext.filesChanged} files, ${fileContext.directories
          .slice(0, 3)
          .join(', ')}`
      : 'no file context';

    let resultMessage = 'NOT SIGNIFICANT (→ skipped_not_significant)';
    if (isSignificant) {
      if (newStatus === 'pending') {
        resultMessage = 'SIGNIFICANT (→ pending)';
      } else if (newStatus === 'analysis_limit_reached') {
        resultMessage =
          'SIGNIFICANT but limit reached (→ analysis_limit_reached)';
      }
    }

    logger.info(
      `[LLM Filter Service] ${item.is_pseudo_pr ? 'Commit' : 'PR'} #${
        item.pr_number
      } (${contextSummary}) - LLM result: ${resultMessage}`
    );

    return { success: true, newStatus };
  } catch (error) {
    logger.error(
      `[LLM Filter Service] Error processing item #${item.pr_number}:`,
      error
    );
    return {
      success: true,
      newStatus: 'pending',
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

export async function updateItemStatus(
  item: PendingEarlyStageItem,
  newStatus: string,
  error?: string
): Promise<boolean> {
  try {
    const updateData: any = {
      status: newStatus,
      last_analyzed_at: new Date().toISOString(),
    };

    if (error) {
      updateData.analysis_error = error;
    }

    const { error: updateError } = await supabaseAdmin
      .from('repository_pr_analysis_status')
      .update(updateData)
      .eq('repository_slug', item.repository_slug)
      .eq('pr_number', item.pr_number)
      .eq('installation_id', item.installation_id);

    if (updateError) {
      logger.error(
        `[LLM Filter Service] Error updating status for item #${item.pr_number}:`,
        updateError
      );
      return false;
    }

    return true;
  } catch (error) {
    logger.error(
      `[LLM Filter Service] Error in updateItemStatus for item #${item.pr_number}:`,
      error
    );
    return false;
  }
}

export async function processBatch(items: PendingEarlyStageItem[]): Promise<{
  processed: number;
  successful: number;
  failed: number;
  pending: number;
  skipped: number;
  limitReached: number;
}> {
  logger.debug(
    `[LLM Filter Service] Processing batch of ${items.length} items...`
  );

  const results = await Promise.all(
    items.map(async item => {
      const llmResult = await processItemWithLlm(item);
      const updateSuccess = await updateItemStatus(
        item,
        llmResult.newStatus,
        llmResult.error
      );

      return {
        item,
        llmResult,
        updateSuccess,
        finalStatus: llmResult.newStatus,
      };
    })
  );

  let successful = 0;
  let failed = 0;
  let pending = 0;
  let skipped = 0;
  let limitReached = 0;

  for (const result of results) {
    if (result.updateSuccess) {
      successful++;
      if (result.finalStatus === 'pending') {
        pending++;
      } else if (result.finalStatus === 'skipped_not_significant') {
        skipped++;
      } else if (result.finalStatus === 'analysis_limit_reached') {
        limitReached++;
      }
    } else {
      failed++;
    }
  }

  logger.info(
    `[LLM Filter Service] Batch complete: ${successful} successful updates, ${failed} failed updates`
  );
  logger.info(
    `[LLM Filter Service] Status distribution: ${pending} → pending, ${skipped} → skipped_not_significant, ${limitReached} → analysis_limit_reached`
  );

  return {
    processed: items.length,
    successful,
    failed,
    pending,
    skipped,
    limitReached,
  };
}
