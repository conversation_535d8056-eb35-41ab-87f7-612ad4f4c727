import {
  supabaseAdmin,
  getAuthToken,
  getRecentCommits,
  getCommitDetail,
  createPseudoPrFromCommit,
  getRecentPRs,
  isDefinitelyNotArchitectural,
  getRepositorySettings,
} from '../utils';
import { RepositoryLoadingJob } from '../types';

const BATCH_SIZE = 100;

/**
 * Check if we should continue loading items for analysis based on repository settings
 */
export async function shouldContinueLoading(
  repositorySlug: string,
  installationId: number
): Promise<{
  shouldContinue: boolean;
  maxLimit: number;
  currentCount: number;
  deepAnalyzedCount: number;
}> {
  try {
    const { data: settings, error: settingsError } =
      await getRepositorySettings(repositorySlug);
    if (settingsError) {
      console.error(
        `[LoaderService] Error fetching repository settings for ${repositorySlug}:`,
        settingsError
      );
      return {
        shouldContinue: true,
        maxLimit: 500,
        currentCount: 0,
        deepAnalyzedCount: 0,
      };
    }

    const maxAnalysis = settings?.max_commits_deep_analysis || 500;

    const { count: deepAnalyzedCount, error: deepCountError } =
      await supabaseAdmin
        .from('repository_pr_analysis_status')
        .select('*', { count: 'exact', head: true })
        .eq('repository_slug', repositorySlug)
        .eq('installation_id', installationId)
        .in('status', ['completed', 'no_decisions', 'failed']);

    if (deepCountError) {
      console.error(
        `[LoaderService] Error counting deep analyzed items for ${repositorySlug}:`,
        deepCountError
      );
      return {
        shouldContinue: true,
        maxLimit: maxAnalysis,
        currentCount: 0,
        deepAnalyzedCount: 0,
      };
    }

    const { count: candidateCount, error: candidateCountError } =
      await supabaseAdmin
        .from('repository_pr_analysis_status')
        .select('*', { count: 'exact', head: true })
        .eq('repository_slug', repositorySlug)
        .eq('installation_id', installationId)
        .in('status', [
          'pending_early_stage',
          'pending',
          'skipped_not_significant',
          'completed',
          'no_decisions',
          'failed',
        ]);

    if (candidateCountError) {
      console.error(
        `[LoaderService] Error counting candidate items for ${repositorySlug}:`,
        candidateCountError
      );
      return {
        shouldContinue: true,
        maxLimit: maxAnalysis,
        currentCount: 0,
        deepAnalyzedCount: deepAnalyzedCount || 0,
      };
    }

    const currentCount = candidateCount || 0;
    const shouldContinue = currentCount < maxAnalysis;

    console.log(
      `[LoaderService] Analysis limit check for ${repositorySlug}: ${currentCount}/${maxAnalysis} candidates loaded (${
        deepAnalyzedCount || 0
      } deep analyzed) - continue: ${shouldContinue}`
    );

    return {
      shouldContinue,
      maxLimit: maxAnalysis,
      currentCount,
      deepAnalyzedCount: deepAnalyzedCount || 0,
    };
  } catch (error) {
    console.error(
      `[LoaderService] Error checking analysis limit for ${repositorySlug}:`,
      error
    );
    return {
      shouldContinue: true,
      maxLimit: 500,
      currentCount: 0,
      deepAnalyzedCount: 0,
    };
  }
}

export async function getNextPendingJob(): Promise<RepositoryLoadingJob | null> {
  const { data, error } = await supabaseAdmin
    .from('repository_loading_jobs')
    .select('*')
    .eq('is_completed', false)
    .eq('status', 'pending')
    .order('updated_at', { ascending: true })
    .limit(1)
    .single();

  if (error && error.code !== 'PGRST116') {
    console.error('[LoaderService] Error getting next pending job:', error);
    return null;
  }
  return data;
}

export async function markJobAsProcessing(jobId: number): Promise<boolean> {
  const { error } = await supabaseAdmin
    .from('repository_loading_jobs')
    .update({
      status: 'processing',
      last_processed_at: new Date().toISOString(),
    })
    .eq('id', jobId);
  if (error) {
    console.error(
      `[LoaderService] Error marking job ${jobId} as processing:`,
      error
    );
    return false;
  }
  return true;
}

export async function updateJobProgress(
  jobId: number,
  updates: Partial<RepositoryLoadingJob>
): Promise<boolean> {
  const { error } = await supabaseAdmin
    .from('repository_loading_jobs')
    .update({
      ...updates,
      status: 'pending',
      updated_at: new Date().toISOString(),
    })
    .eq('id', jobId);

  if (error) {
    console.error(
      `[LoaderService] Error updating job ${jobId} progress:`,
      error
    );
    return false;
  }
  return true;
}

export async function processPrBatch(
  job: RepositoryLoadingJob,
  octokit: any
): Promise<{ success: boolean; itemsAdded: number; hasMore: boolean }> {
  const [owner, repo] = job.repository_slug.split('/');
  console.log(
    `[LoaderService][PRs] Processing PRs for ${job.repository_slug}, page ${job.next_page_to_process}`
  );

  const { shouldContinue, maxLimit, currentCount } =
    await shouldContinueLoading(job.repository_slug, job.installation_id);
  if (!shouldContinue) {
    console.log(
      `[LoaderService][PRs] Stopping PR loading for ${job.repository_slug} - analysis limit reached (${currentCount}/${maxLimit})`
    );
    return { success: true, itemsAdded: 0, hasMore: false };
  }

  const effectiveInstallationId =
    job.installation_id === 0 ? undefined : job.installation_id.toString();
  const authToken = await getAuthToken(effectiveInstallationId);

  const result = await getRecentPRs(
    owner,
    repo,
    {
      per_page: BATCH_SIZE,
      page: job.next_page_to_process,
      returnPaginationInfo: true,
    },
    authToken as any
  );

  const {
    prs,
    hasMore: hasMoreFromGitHub,
    totalFromGitHub,
    mergedCount,
  } = result as {
    prs: any[];
    hasMore: boolean;
    totalFromGitHub: number;
    mergedCount: number;
    filteredCount: number;
  };

  console.log(
    `[LoaderService][PRs] Fetched ${totalFromGitHub} total PRs from GitHub, ${mergedCount} merged PRs for ${job.repository_slug}. HasMore: ${hasMoreFromGitHub}`
  );

  if (prs.length === 0) {
    console.log(
      `[LoaderService][PRs] No more PRs found for ${job.repository_slug}.`
    );
    return { success: true, itemsAdded: 0, hasMore: hasMoreFromGitHub };
  }

  const prItemsToAdd = prs
    .filter(pr => pr.merged_at && !isDefinitelyNotArchitectural(pr.title))
    .map(pr => ({
      installation_id: job.installation_id,
      repository_slug: job.repository_slug,
      pr_number: pr.number,
      pr_title: pr.title.substring(0, 255),
      pr_url: pr.html_url,
      pr_created_at: pr.created_at,
      pr_merged_at: pr.merged_at,
      is_pseudo_pr: false,
      status: 'pending_early_stage',
      commit_sha: pr.merge_commit_sha,
    }));

  console.log(
    `[LoaderService][PRs] After filtering, ${prItemsToAdd.length} PRs will be upserted.`
  );

  if (prItemsToAdd.length > 0) {
    const { error } = await supabaseAdmin
      .from('repository_pr_analysis_status')
      .upsert(prItemsToAdd, {
        onConflict: 'repository_slug, pr_number, installation_id',
        ignoreDuplicates: true,
      });

    if (error) {
      console.error('[LoaderService][PRs] Error upserting PRs:', error);
    } else {
      console.log(
        `[LoaderService][PRs] Successfully upserted ${prItemsToAdd.length} PRs.`
      );
    }

    // Store commit SHAs
    const prCommitShas: any[] = [];
    for (const prItem of prItemsToAdd) {
      if (prItem.commit_sha) {
        prCommitShas.push({
          repository_slug: prItem.repository_slug,
          installation_id: prItem.installation_id,
          commit_sha: prItem.commit_sha,
        });
      }

      try {
        const prCommits = await octokit.rest.pulls.listCommits({
          owner,
          repo,
          pull_number: prItem.pr_number,
          per_page: 100,
        });

        for (const commit of prCommits.data) {
          if (commit.sha && commit.sha !== prItem.commit_sha) {
            prCommitShas.push({
              repository_slug: prItem.repository_slug,
              installation_id: prItem.installation_id,
              commit_sha: commit.sha,
            });
          }
        }
      } catch (commitError) {
        console.warn(
          `[LoaderService][PRs] Could not fetch commits for PR #${prItem.pr_number}:`,
          commitError
        );
      }
    }

    if (prCommitShas.length > 0) {
      const { error: commitError } = await supabaseAdmin
        .from('repository_pr_commit_shas')
        .upsert(prCommitShas, {
          onConflict: 'repository_slug, installation_id, commit_sha',
          ignoreDuplicates: true,
        });

      if (commitError) {
        console.error(
          '[LoaderService][PRs] Error storing PR commit SHAs:',
          commitError
        );
      } else {
        console.log(
          `[LoaderService][PRs] Stored ${prCommitShas.length} commit SHAs for PRs.`
        );
      }
    }
  }

  return {
    success: true,
    itemsAdded: prItemsToAdd.length,
    hasMore: hasMoreFromGitHub,
  };
}

export async function processCommitBatch(
  job: RepositoryLoadingJob,
  authToken: string | null
): Promise<{ success: boolean; itemsAdded: number; hasMore: boolean }> {
  const [owner, repo] = job.repository_slug.split('/');
  console.log(
    `[LoaderService][Commits] Processing commits for ${job.repository_slug}, page ${job.next_page_to_process}`
  );

  const { shouldContinue, maxLimit, currentCount } =
    await shouldContinueLoading(job.repository_slug, job.installation_id);
  if (!shouldContinue) {
    console.log(
      `[LoaderService][Commits] Stopping commit loading for ${job.repository_slug} - analysis limit reached (${currentCount}/${maxLimit})`
    );
    return { success: true, itemsAdded: 0, hasMore: false };
  }

  const [prCommitShas, existingCommitShas] = await Promise.all([
    getPrCommitShas(job.repository_slug, job.installation_id),
    getExistingCommitShas(job.repository_slug, job.installation_id),
  ]);

  console.log(
    `[LoaderService][Commits] Found ${prCommitShas.size} PR commit SHAs and ${existingCommitShas.size} existing standalone commit SHAs to skip.`
  );

  const commits = await getRecentCommits(
    owner,
    repo,
    {
      maxCommits: BATCH_SIZE,
      startPage: job.next_page_to_process,
      sinceDate: '',
      branch: '',
    },
    authToken
  );

  console.log(
    `[LoaderService][Commits] Fetched ${commits.length} commits from GitHub for ${job.repository_slug}.`
  );

  if (commits.length === 0) {
    console.log(
      `[LoaderService][Commits] No more commits found for ${job.repository_slug}.`
    );
    return { success: true, itemsAdded: 0, hasMore: false };
  }

  const newCommits = commits.filter(
    (commit: any) =>
      !prCommitShas.has(commit.sha) && !existingCommitShas.has(commit.sha)
  );
  console.log(
    `[LoaderService][Commits] ${newCommits.length} commits are new (not associated with PRs or already processed standalone).`
  );

  const commitItemsToAdd: any[] = [];

  for (const commit of newCommits) {
    if (
      commit.message.startsWith('Merge ') ||
      isDefinitelyNotArchitectural(commit.message)
    ) {
      continue;
    }

    try {
      const commitDetail: any = await getCommitDetail(
        owner,
        repo,
        commit.sha,
        authToken
      );

      if (!commitDetail.files || commitDetail.files.length === 0) {
        continue;
      }

      const hasCodeChanges = commitDetail.files.some((file: any) => {
        const filename = file.filename.toLowerCase();
        return !filename.match(/\.(md|txt|json|yml|yaml|xml|config|ini|log)$/);
      });

      if (!hasCodeChanges) {
        continue;
      }

      const pseudoPr: any = createPseudoPrFromCommit(
        commit,
        commitDetail,
        owner,
        repo
      );
      commitItemsToAdd.push({
        installation_id: job.installation_id,
        repository_slug: job.repository_slug,
        pr_number: pseudoPr.number,
        pr_title: pseudoPr.title.substring(0, 255),
        pr_url: pseudoPr.html_url,
        pr_created_at: pseudoPr.created_at,
        pr_merged_at: pseudoPr.merged_at,
        is_pseudo_pr: true,
        status: 'pending_early_stage',
        commit_sha: commit.sha,
      });
    } catch (error) {
      console.warn(
        `[LoaderService][Commits] Error getting details for commit ${commit.sha}:`,
        error
      );
    }
  }

  console.log(
    `[LoaderService][Commits] After filtering, ${commitItemsToAdd.length} commits will be upserted as pseudo-PRs.`
  );

  if (commitItemsToAdd.length > 0) {
    const { error } = await supabaseAdmin
      .from('repository_pr_analysis_status')
      .upsert(commitItemsToAdd, {
        onConflict: 'repository_slug, pr_number, installation_id',
        ignoreDuplicates: true,
      });

    if (error) {
      console.error('[LoaderService][Commits] Error upserting commits:', error);
    } else {
      console.log(
        `[LoaderService][Commits] Successfully upserted ${commitItemsToAdd.length} commits as pseudo-PRs.`
      );
    }
  }

  return {
    success: true,
    itemsAdded: commitItemsToAdd.length,
    hasMore: commits.length === BATCH_SIZE,
  };
}

async function getPrCommitShas(
  repositorySlug: string,
  installationId: number
): Promise<Set<string>> {
  const { data: prCommitData, error: prCommitError } = await supabaseAdmin
    .from('repository_pr_commit_shas')
    .select('commit_sha')
    .eq('repository_slug', repositorySlug)
    .eq('installation_id', installationId);

  if (prCommitError) {
    console.error(
      `[LoaderService] Error fetching PR commit SHAs:`,
      prCommitError
    );
  }

  const { data: statusData, error: statusError } = await supabaseAdmin
    .from('repository_pr_analysis_status')
    .select('commit_sha')
    .eq('repository_slug', repositorySlug)
    .eq('installation_id', installationId)
    .not('commit_sha', 'is', null);

  if (statusError) {
    console.error(
      `[LoaderService] Error fetching commit SHAs from analysis status:`,
      statusError
    );
  }

  const allCommitShas = new Set<string>();

  if (prCommitData) {
    prCommitData.forEach((item: any) => allCommitShas.add(item.commit_sha));
  }

  if (statusData) {
    statusData.forEach(
      (item: any) => item.commit_sha && allCommitShas.add(item.commit_sha)
    );
  }

  console.log(
    `[LoaderService] Found ${allCommitShas.size} total commit SHAs to skip (${
      prCommitData?.length || 0
    } from PR commits table, ${
      statusData?.length || 0
    } from analysis status table)`
  );

  return allCommitShas;
}

async function getExistingCommitShas(
  repositorySlug: string,
  installationId: number
): Promise<Set<string>> {
  const { data, error } = await supabaseAdmin
    .from('repository_pr_analysis_status')
    .select('commit_sha')
    .eq('repository_slug', repositorySlug)
    .eq('installation_id', installationId)
    .eq('is_pseudo_pr', true)
    .not('commit_sha', 'is', null);

  if (error) {
    console.error(
      `[LoaderService] Error fetching existing standalone commit SHAs:`,
      error
    );
    return new Set();
  }
  return new Set(data?.map((item: any) => item.commit_sha) || []);
}
