import express from 'express';
import cors from 'cors';
import compression from 'compression';
import helmet from 'helmet';
import config from './config';
import { loggerMiddleware, errorHandler } from './middlewares';
import analysisRoutes from './routes/analysis.routes';

const app = express();

// Security middleware
app.use(helmet());

// Enable CORS
app.use(cors(config.cors));

// Enable GZIP compression
app.use(compression());

// Request logging
app.use(loggerMiddleware);

// Body parsing middleware
app.use(express.json({ limit: config.requestLimits.json }));
app.use(
  express.urlencoded({ extended: true, limit: config.requestLimits.urlencoded })
);

// Routes
app.use('/api/analysis', analysisRoutes);

// Root endpoint
app.get('/', (req, res) => {
  res.send('Nuvineer Analysis Worker Server - Ready!');
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    environment: config.environment,
    endpoints: [
      '/api/analysis/repository-loader-job',
      '/api/analysis/llm-filter-job',
      '/api/analysis/process-job',
    ],
  });
});

// Error handling middleware (must be last)
app.use(errorHandler);

export default app;
