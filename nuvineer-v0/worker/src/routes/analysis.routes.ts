import { Router } from 'express';
import { cronAuthMiddleware } from '../middlewares';
import {
  handleRepositoryLoaderJob,
  getRepositoryLoaderStatus,
} from '../controllers/repositoryLoaderController';
import {
  handleLlmFilterJob,
  getLlmFilterStatus,
} from '../controllers/llmFilterController';
import {
  handleProcessJob,
  getProcessJobStatus,
} from '../controllers/processJobController';

const router = Router();

// Repository loader job endpoints
router.post(
  '/repository-loader-job',
  cronAuthMiddleware,
  handleRepositoryLoaderJob
);
router.get(
  '/repository-loader-job-status',
  cronAuthMiddleware,
  getRepositoryLoaderStatus
);

// LLM filter job endpoints
router.post('/llm-filter-job', cronAuthMiddleware, handleLlmFilterJob);
router.get('/llm-filter-job-status', cronAuthMiddleware, getLlmFilterStatus);

// Process job endpoints
router.post('/process-job', cronAuthMiddleware, handleProcessJob);
router.get('/process-job-status', cronAuthMiddleware, getProcessJobStatus);

export default router;
