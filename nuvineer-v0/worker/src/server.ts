import app from './app';
import config from './config';

const server = app.listen(config.port, () => {
  console.log(
    `🚀 Nuvineer Analysis Worker running at http://localhost:${config.port}`
  );
  console.log(`🌍 Environment: ${config.environment}`);
  console.log(`🌍 Database URL: ${config.supabase.url}`);
  console.log(`📊 Available endpoints:`);
  console.log(`   - POST   /repository-loader-job`);
  console.log(`   - GET    /repository-loader-job-status`);
  console.log(`   - POST   /llm-filter-job`);
  console.log(`   - GET    /llm-filter-job-status`);
  console.log(`   - POST   /process-job`);
  console.log(`   - GET    /process-job-status`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received. Shutting down gracefully...');
  server.close(() => {
    console.log('Server closed.');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received. Shutting down gracefully...');
  server.close(() => {
    console.log('Server closed.');
    process.exit(0);
  });
});

export default server;
