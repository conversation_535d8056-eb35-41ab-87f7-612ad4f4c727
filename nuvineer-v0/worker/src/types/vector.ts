// Vector Database Types

export interface VectorSearchResult {
  id: string;
  score: number;
  metadata: Record<string, any>;
  values?: number[];
}

export interface VectorRecord {
  id: string;
  values: number[];
  metadata: Record<string, any>;
}

export interface VectorQueryOptions {
  vector: number[];
  topK?: number;
  minScore?: number;
  filter?: Record<string, any>;
  includeValues?: boolean;
  includeMetadata?: boolean;
}

export interface VectorUpsertRecord {
  id: string;
  values: number[];
  metadata: Record<string, any>;
}

export interface VectorDatabaseStats {
  dimension: number;
  indexFullness: number;
  namespaces: Record<string, { vectorCount: number }>;
}

export type VectorDatabaseType = 'pinecone' | 'pgvector';

export interface VectorDatabaseConfig {
  type: VectorDatabaseType;
  namespace: string;
}
