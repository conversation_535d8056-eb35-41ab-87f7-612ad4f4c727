// Worker-specific types for job processing and responses

// Common response types for analysis jobs
export interface LoaderResponse {
  action:
    | 'processed_batch'
    | 'switched_phase'
    | 'completed_job'
    | 'no_jobs'
    | 'error'
    | 'skipped_concurrency';
  message: string;
  details?: any;
}

export interface LlmFilterResponse {
  action: 'processed_items' | 'no_action' | 'error';
  message: string;
  details?: any;
}

export interface ProcessResponse {
  action:
    | 'processed_prs_batch'
    | 'no_action'
    | 'error'
    | 'activated_skipped_prs';
  message: string;
  details?: any;
}

// Database types for worker jobs
export interface RepositoryLoadingJob {
  id: number;
  repository_slug: string;
  installation_id: number;
  current_phase: 'prs' | 'commits' | 'completed';
  next_page_to_process: number;
  is_completed: boolean;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  error_message?: string;
}

export interface PendingEarlyStageItem {
  id: number;
  repository_slug: string;
  installation_id: number;
  pr_number: number;
  pr_title: string;
  pr_url: string;
  pr_created_at: string;
  pr_merged_at: string;
  is_pseudo_pr: boolean;
  commit_sha?: string;
}

export interface PendingPR {
  pr_number: number | string;
  pr_title: string;
  pr_url: string;
  pr_merged_at: string;
  repository_slug: string;
  installation_id: number;
  is_pseudo_pr?: boolean;
  commit_sha?: string;
}
