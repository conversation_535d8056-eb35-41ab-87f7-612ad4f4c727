// GitHub-related types for worker utilities

export interface PRDetails {
  number: number;
  title: string;
  body: string;
  state: string;
  author: string;
  created_at: string;
  merged_at: string | null;
  merged: boolean;
  html_url: string;
  head: {
    sha: string;
    ref: string;
  };
  base: {
    sha: string;
    ref: string;
  };
}

export interface PRFile {
  filename: string;
  status: string;
  additions: number;
  deletions: number;
  changes: number;
  patch: string;
  key_changes?: string[];
}

export interface PRComment {
  id: number;
  body: string;
  user: {
    login: string;
  };
  created_at: string;
  updated_at: string;
  html_url: string;
}

export interface RecentPRsOptions {
  maxPRs?: number;
  count?: number;
  branch?: string | null;
  sinceDate?: string | null;
  page?: number;
  per_page?: number;
  state?: string;
  sort?: string;
  direction?: string;
  returnPaginationInfo?: boolean;
}

export interface RecentPRsResponse {
  prs: any[];
  hasMore: boolean;
  totalFromGitHub: number;
  mergedCount: number;
  filteredCount: number;
}

export interface RecentCommitsOptions {
  maxCommits?: number;
  sinceDate?: string | null;
  branch?: string | null;
  startPage?: number;
}

export interface CommitDetail {
  sha: string;
  commit: {
    author: {
      name: string;
      email: string;
      date: string;
    };
    message: string;
  };
  author?: {
    login: string;
  };
  html_url: string;
  files: Array<{
    filename: string;
    additions: number;
    deletions: number;
    patch?: string;
    status: string;
  }>;
}

export interface PseudoPR {
  prContext: {
    title: string;
    body: string;
    html_url: string;
    number: string;
    merged_at: string;
    created_at: string;
    user: {
      login: string;
    };
    files: PRFile[];
    is_pseudo_pr: boolean;
    commit_sha: string;
  };
  codeChanges: Array<{
    filename: string;
    patch: string;
  }>;
  comments: any[];
}
