// Repository settings types for worker utilities

export interface RepositorySettings {
  id?: string;
  repository_slug: string;
  max_commits_deep_analysis: number;
  tech_debt_analysis_enabled: boolean;
  vector_db?: 'pinecone' | 'pgvector';
  created_at?: string;
  updated_at?: string;
}

export interface CreateRepositorySettingsRequest {
  repository_slug: string;
  max_commits_deep_analysis?: number;
  tech_debt_analysis_enabled?: boolean;
  vector_db?: 'pinecone' | 'pgvector';
}

export interface UpdateRepositorySettingsRequest {
  max_commits_deep_analysis?: number;
  tech_debt_analysis_enabled?: boolean;
  vector_db?: 'pinecone' | 'pgvector';
}
