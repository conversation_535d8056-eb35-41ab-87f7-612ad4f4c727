// Vector Database Base Classes and Utilities

import {
  VectorSearchResult,
  VectorRecord,
  VectorQueryOptions,
  VectorUpsertRecord,
  VectorDatabaseStats,
  VectorDatabaseType,
  VectorDatabaseConfig,
} from '../../types/vector';

/**
 * Abstract base class for vector database implementations
 */
export abstract class VectorDatabase {
  protected namespace: string;

  constructor(namespace: string) {
    this.namespace = namespace;
  }

  /**
   * Query vectors by similarity
   */
  abstract query(options: VectorQueryOptions): Promise<VectorSearchResult[]>;

  /**
   * Upsert (insert or update) vectors
   */
  abstract upsert(vectors: VectorUpsertRecord[]): Promise<void>;

  /**
   * Fetch vectors by IDs
   */
  abstract fetch(ids: string[]): Promise<Record<string, VectorRecord>>;

  /**
   * Delete vectors by IDs
   */
  abstract delete(ids: string[]): Promise<void>;

  /**
   * Get database statistics
   */
  abstract getStats(): Promise<VectorDatabaseStats>;

  /**
   * Update vector metadata without changing the embedding
   */
  abstract updateMetadata(
    id: string,
    metadata: Record<string, any>
  ): Promise<void>;

  /**
   * Check if namespace exists
   */
  abstract namespaceExists(): Promise<boolean>;

  /**
   * Get the namespace being used
   */
  getNamespace(): string {
    return this.namespace;
  }
}

/**
 * Factory for creating vector database instances based on configuration
 */
export class VectorDatabaseFactory {
  static async create(config: VectorDatabaseConfig): Promise<VectorDatabase> {
    switch (config.type) {
      case 'pinecone':
        const { PineconeVectorDatabase } = await import('./pineconeAdapter');
        return new PineconeVectorDatabase(config.namespace);

      case 'pgvector':
        const { PgVectorDatabase } = await import('./pgvectorAdapter');
        return new PgVectorDatabase(config.namespace);

      default:
        throw new Error(`Unsupported vector database type: ${config.type}`);
    }
  }
}

/**
 * Utility functions for vector operations
 */
export class VectorUtils {
  /**
   * Validate that a vector has the expected dimensions
   */
  static validateDimensions(
    vector: number[],
    expectedDimension: number
  ): boolean {
    return vector.length === expectedDimension;
  }

  /**
   * Normalize metadata for vector database compatibility
   * Both Pinecone and pgvector have restrictions on metadata values
   */
  static sanitizeMetadata(metadata: Record<string, any>): Record<string, any> {
    const sanitized: Record<string, any> = {};

    for (const [key, value] of Object.entries(metadata)) {
      if (value === null || value === undefined) {
        continue;
      }

      // Handle field name mapping for backward compatibility
      let targetKey = key;
      if (key === 'risks') {
        targetKey = 'risks_extracted'; // Map risks -> risks_extracted for backward compatibility
      }

      // Handle different value types
      if (
        typeof value === 'string' ||
        typeof value === 'number' ||
        typeof value === 'boolean'
      ) {
        sanitized[targetKey] = value;
      } else if (Array.isArray(value)) {
        // Check if array contains objects (like risks) or primitives (like related_files)
        const filteredValue = value.filter(v => v !== null && v !== undefined);
        if (filteredValue.length > 0 && typeof filteredValue[0] === 'object') {
          // Array of objects - serialize as JSON
          sanitized[targetKey] = JSON.stringify(filteredValue);
        } else {
          // Array of primitives - join as comma-separated string
          sanitized[targetKey] = filteredValue.join(', ');
        }
      } else if (typeof value === 'object') {
        // Convert objects to JSON strings
        sanitized[targetKey] = JSON.stringify(value);
      } else {
        // Convert other types to strings
        sanitized[targetKey] = String(value);
      }
    }

    return sanitized;
  }

  /**
   * Convert similarity score between different vector database formats
   * Pinecone uses cosine similarity (0-1), pgvector may use different metrics
   */
  static normalizeScore(score: number, sourceType: VectorDatabaseType): number {
    switch (sourceType) {
      case 'pinecone':
        return score; // Already 0-1 cosine similarity
      case 'pgvector':
        // pgvector cosine distance is 1 - cosine_similarity, so we convert back
        return Math.max(0, 1 - score);
      default:
        return score;
    }
  }
}

/**
 * Error classes for vector database operations
 */
export class VectorDatabaseError extends Error {
  constructor(
    message: string,
    public readonly operation: string,
    public readonly cause?: Error
  ) {
    super(message);
    this.name = 'VectorDatabaseError';
  }
}

export class VectorNotFoundError extends VectorDatabaseError {
  constructor(id: string, namespace: string) {
    super(
      `Vector with ID '${id}' not found in namespace '${namespace}'`,
      'fetch'
    );
    this.name = 'VectorNotFoundError';
  }
}

export class NamespaceNotFoundError extends VectorDatabaseError {
  constructor(namespace: string) {
    super(`Namespace '${namespace}' does not exist`, 'namespace_check');
    this.name = 'NamespaceNotFoundError';
  }
}
