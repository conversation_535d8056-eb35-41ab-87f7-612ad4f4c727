// Pinecone Vector Database Adapter

import { Pinecone, Index } from '@pinecone-database/pinecone';
import {
  VectorSearchResult,
  VectorRecord,
  VectorQueryOptions,
  VectorUpsertRecord,
  VectorDatabaseStats,
} from '../../types/vector';
import {
  VectorDatabase,
  VectorUtils,
  VectorDatabaseError,
  VectorNotFoundError,
} from './base';
import { pineconeClient } from '../pineconeClient';
import config from '../../config';

export class PineconeVectorDatabase extends VectorDatabase {
  private pinecone: Pinecone;
  private index: Index;
  private indexName: string;
  private static indexCache: Record<string, Index> = {};

  constructor(namespace: string) {
    super(namespace);

    if (!pineconeClient) {
      const errorMessage =
        'Pinecone client not configured. Please set PINECONE_API_KEY environment variable.';
      throw new VectorDatabaseError(errorMessage, 'initialization');
    }

    // Use the shared Pinecone client and config
    this.pinecone = pineconeClient;
    this.indexName = config.pinecone.indexName;

    // Use cached index or create new one
    if (!PineconeVectorDatabase.indexCache[this.indexName]) {
      PineconeVectorDatabase.indexCache[this.indexName] = this.pinecone.Index(
        this.indexName
      );
    }
    this.index = PineconeVectorDatabase.indexCache[this.indexName];
  }

  async query(options: VectorQueryOptions): Promise<VectorSearchResult[]> {
    try {
      const {
        vector,
        topK,
        minScore = 0.7,
        filter,
        includeMetadata = true,
        includeValues = false,
      } = options;

      // Apply default filter for non-superseded decisions if no filter provided
      let finalFilter = filter;
      if (!filter) {
        finalFilter = { is_superseded: false };
      }

      const queryOptions: any = {
        vector,
        topK,
        includeMetadata,
        includeValues,
      };

      if (finalFilter) {
        queryOptions.filter = finalFilter;
      }

      console.log(
        `[PineconeAdapter] Querying namespace '${this.namespace}' (topK: ${topK}, minScore: ${minScore})`
      );

      const queryResponse = await this.index
        .namespace(this.namespace)
        .query(queryOptions);
      const matches = queryResponse.matches || [];

      // Filter by minimum score and map to our interface
      const filteredMatches = matches
        .filter((match: any) => match.score && match.score >= minScore)
        .map((match: any) => ({
          id: match.id,
          score: match.score || 0,
          metadata: match.metadata || {},
        }));

      console.log(
        `[PineconeAdapter] Found ${matches.length} raw matches, ${filteredMatches.length} after score filtering`
      );
      return filteredMatches;
    } catch (error) {
      console.error(
        `[PineconeAdapter] Error querying namespace '${this.namespace}':`,
        error
      );
      throw new VectorDatabaseError(
        `Query failed: ${error instanceof Error ? error.message : String(error)}`,
        'query',
        error instanceof Error ? error : undefined
      );
    }
  }

  async upsert(vectors: VectorUpsertRecord[]): Promise<void> {
    try {
      console.log(
        `[PineconeAdapter] Upserting ${vectors.length} vectors to namespace '${this.namespace}'`
      );

      // Sanitize metadata for all vectors
      const sanitizedVectors = vectors.map(vector => ({
        ...vector,
        metadata: VectorUtils.sanitizeMetadata(vector.metadata),
      }));

      await this.index.namespace(this.namespace).upsert(sanitizedVectors);
      console.log(
        `[PineconeAdapter] Successfully upserted ${vectors.length} vectors`
      );
    } catch (error) {
      console.error(
        `[PineconeAdapter] Error upserting vectors to namespace '${this.namespace}':`,
        error
      );
      throw new VectorDatabaseError(
        `Upsert failed: ${error instanceof Error ? error.message : String(error)}`,
        'upsert',
        error instanceof Error ? error : undefined
      );
    }
  }

  async fetch(ids: string[]): Promise<Record<string, VectorRecord>> {
    try {
      console.log(
        `[PineconeAdapter] Fetching ${ids.length} vectors from namespace '${this.namespace}'`
      );

      const fetchResponse = await this.index
        .namespace(this.namespace)
        .fetch(ids);
      const records: Record<string, VectorRecord> = {};

      for (const [id, vector] of Object.entries(fetchResponse.records || {})) {
        records[id] = {
          id,
          values: (vector as any).values,
          metadata: (vector as any).metadata || {},
        };
      }

      console.log(
        `[PineconeAdapter] Successfully fetched ${Object.keys(records).length} vectors`
      );
      return records;
    } catch (error) {
      console.error(
        `[PineconeAdapter] Error fetching vectors from namespace '${this.namespace}':`,
        error
      );
      throw new VectorDatabaseError(
        `Fetch failed: ${error instanceof Error ? error.message : String(error)}`,
        'fetch',
        error instanceof Error ? error : undefined
      );
    }
  }

  async delete(ids: string[]): Promise<void> {
    try {
      console.log(
        `[PineconeAdapter] Deleting ${ids.length} vectors from namespace '${this.namespace}'`
      );

      await this.index.namespace(this.namespace).deleteMany(ids);
      console.log(
        `[PineconeAdapter] Successfully deleted ${ids.length} vectors`
      );
    } catch (error) {
      console.error(
        `[PineconeAdapter] Error deleting vectors from namespace '${this.namespace}':`,
        error
      );
      throw new VectorDatabaseError(
        `Delete failed: ${error instanceof Error ? error.message : String(error)}`,
        'delete',
        error instanceof Error ? error : undefined
      );
    }
  }

  async updateMetadata(
    id: string,
    metadata: Record<string, any>
  ): Promise<void> {
    try {
      console.log(
        `[PineconeAdapter] Updating metadata for vector '${id}' in namespace '${this.namespace}'`
      );

      // Fetch the existing vector to get its values
      const existing = await this.fetch([id]);
      if (!existing[id]) {
        throw new VectorNotFoundError(id, this.namespace);
      }

      const sanitizedMetadata = VectorUtils.sanitizeMetadata(metadata);

      // Upsert with existing values and new metadata
      await this.upsert([
        {
          id,
          values: existing[id].values || [],
          metadata: { ...existing[id].metadata, ...sanitizedMetadata },
        },
      ]);

      console.log(
        `[PineconeAdapter] Successfully updated metadata for vector '${id}'`
      );
    } catch (error) {
      console.error(
        `[PineconeAdapter] Error updating metadata for vector '${id}':`,
        error
      );
      if (error instanceof VectorNotFoundError) {
        throw error;
      }
      throw new VectorDatabaseError(
        `Update metadata failed: ${error instanceof Error ? error.message : String(error)}`,
        'updateMetadata',
        error instanceof Error ? error : undefined
      );
    }
  }

  async getStats(): Promise<VectorDatabaseStats> {
    try {
      console.log(
        `[PineconeAdapter] Getting stats for index '${this.indexName}'`
      );

      const indexStats = await this.index.describeIndexStats();

      // Convert Pinecone namespace format to our interface format
      const namespaces: Record<string, { vectorCount: number }> = {};
      if (indexStats.namespaces) {
        for (const [namespace, stats] of Object.entries(
          indexStats.namespaces
        )) {
          namespaces[namespace] = {
            vectorCount: (stats as any).recordCount || 0,
          };
        }
      }

      return {
        dimension: indexStats.dimension || 1536,
        indexFullness: indexStats.indexFullness || 0,
        namespaces,
      };
    } catch (error) {
      console.error(`[PineconeAdapter] Error getting stats:`, error);
      throw new VectorDatabaseError(
        `Get stats failed: ${error instanceof Error ? error.message : String(error)}`,
        'getStats',
        error instanceof Error ? error : undefined
      );
    }
  }

  async namespaceExists(): Promise<boolean> {
    try {
      const stats = await this.getStats();
      return this.namespace in (stats.namespaces || {});
    } catch (error) {
      console.error(
        `[PineconeAdapter] Error checking namespace existence:`,
        error
      );
      return false;
    }
  }
}
