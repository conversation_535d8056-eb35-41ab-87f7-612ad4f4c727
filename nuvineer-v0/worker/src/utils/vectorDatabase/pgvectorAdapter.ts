// PostgreSQL + pgvector Database Adapter

import { SupabaseClient } from '@supabase/supabase-js';
import {
  VectorSearchResult,
  VectorRecord,
  VectorQueryOptions,
  VectorUpsertRecord,
  VectorDatabaseStats,
} from '../../types/vector';
import { VectorDatabase, VectorUtils, VectorDatabaseError } from './base';
import { supabaseAdmin } from '../supabaseClient';

export class PgVectorDatabase extends VectorDatabase {
  private supabase: SupabaseClient;
  private tableName: string = 'decision_vectors';

  constructor(namespace: string) {
    super(namespace);

    // Use the shared Supabase client
    this.supabase = supabaseAdmin;

    console.log(`[PgVectorAdapter] Initialized for namespace: ${namespace}`);
  }

  /**
   * Set the namespace context for RLS (Row Level Security)
   */
  private async setNamespaceContext(): Promise<void> {
    try {
      const { error } = await this.supabase.rpc('set_vector_namespace', {
        target_namespace: this.namespace,
      });

      if (error) {
        throw new Error(`Failed to set namespace context: ${error.message}`);
      }
    } catch (error) {
      throw new VectorDatabaseError(
        `Failed to set namespace context for ${this.namespace}: ${error instanceof Error ? error.message : String(error)}`,
        'setNamespace',
        error instanceof Error ? error : undefined
      );
    }
  }

  async query(options: VectorQueryOptions): Promise<VectorSearchResult[]> {
    try {
      // Set namespace context for RLS
      await this.setNamespaceContext();

      const {
        vector,
        topK,
        minScore = 0.7,
        filter = {},
        includeMetadata = true,
        includeValues = false,
      } = options;

      console.log(
        `[PgVectorAdapter] Using vectorSimilaritySearch function for namespace ${this.namespace}`
      );

      // Use the stored PostgreSQL function for more reliable vector search
      const data = await this.vectorSimilaritySearch(
        vector || [],
        topK || 10,
        includeValues,
        includeMetadata,
        filter
      );

      // Convert to VectorSearchResult format
      const results: VectorSearchResult[] = (data || [])
        .filter((row: any) => {
          const score = 1 - row.distance; // Convert distance to similarity score
          return score >= minScore; // Filter by minimum score first
        })
        .map((row: any) => {
          const score = 1 - row.distance; // Convert distance to similarity score

          return {
            id: row.id,
            score: Math.max(0, Math.min(1, score)), // Clamp between 0 and 1
            metadata: includeMetadata ? row.metadata : {},
            values: includeValues ? row.embedding : [],
          };
        });

      console.log(
        `[PgVectorAdapter] Found ${results.length} results after filtering for namespace ${this.namespace}`
      );
      return results;
    } catch (error) {
      console.error(
        `[PgVectorAdapter] Error querying namespace '${this.namespace}':`,
        error
      );
      throw new VectorDatabaseError(
        `Query failed: ${error instanceof Error ? error.message : String(error)}`,
        'query',
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Helper function to perform vector similarity search using stored function
   */
  private async vectorSimilaritySearch(
    queryVector: number[],
    limit: number,
    includeValues: boolean,
    includeMetadata: boolean,
    filter: Record<string, any>
  ): Promise<any[]> {
    try {
      // Set namespace context for RLS
      await this.setNamespaceContext();

      console.log(
        `[PgVectorAdapter] Calling vector_similarity_search function`
      );

      const { data, error } = await this.supabase.rpc(
        'vector_similarity_search',
        {
          query_vector: queryVector,
          similarity_threshold: 0,
          max_results: limit,
          table_filter: filter || {},
        }
      );

      if (error) {
        throw new Error(`Vector similarity search failed: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      console.error(
        `[PgVectorAdapter] Error in vectorSimilaritySearch:`,
        error
      );
      throw error;
    }
  }

  async upsert(vectors: VectorUpsertRecord[]): Promise<void> {
    try {
      // Set namespace context for RLS
      await this.setNamespaceContext();

      console.log(
        `[PgVectorAdapter] Upserting ${vectors.length} vectors to namespace '${this.namespace}'`
      );

      // Prepare vectors for insertion with sanitized metadata
      const vectorsToInsert = vectors.map(vector => ({
        id: vector.id,
        embedding: vector.values,
        metadata: VectorUtils.sanitizeMetadata({
          ...vector.metadata,
          namespace: this.namespace,
        }),
        namespace: this.namespace,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }));

      // Use upsert to handle both inserts and updates
      const { error } = await this.supabase
        .from(this.tableName)
        .upsert(vectorsToInsert, {
          onConflict: 'id,namespace',
        });

      if (error) {
        throw new Error(`Failed to upsert vectors: ${error.message}`);
      }

      console.log(
        `[PgVectorAdapter] Successfully upserted ${vectors.length} vectors`
      );
    } catch (error) {
      console.error(
        `[PgVectorAdapter] Error upserting vectors to namespace '${this.namespace}':`,
        error
      );
      throw new VectorDatabaseError(
        `Upsert failed: ${error instanceof Error ? error.message : String(error)}`,
        'upsert',
        error instanceof Error ? error : undefined
      );
    }
  }

  async fetch(ids: string[]): Promise<Record<string, VectorRecord>> {
    try {
      // Set namespace context for RLS
      await this.setNamespaceContext();

      console.log(
        `[PgVectorAdapter] Fetching ${ids.length} vectors from namespace '${this.namespace}'`
      );

      const { data, error } = await this.supabase
        .from(this.tableName)
        .select('id, embedding, metadata')
        .in('id', ids)
        .eq('namespace', this.namespace);

      if (error) {
        throw new Error(`Failed to fetch vectors: ${error.message}`);
      }

      const records: Record<string, VectorRecord> = {};
      for (const row of data || []) {
        records[row.id] = {
          id: row.id,
          values: row.embedding,
          metadata: row.metadata || {},
        };
      }

      console.log(
        `[PgVectorAdapter] Successfully fetched ${Object.keys(records).length} vectors`
      );
      return records;
    } catch (error) {
      console.error(
        `[PgVectorAdapter] Error fetching vectors from namespace '${this.namespace}':`,
        error
      );
      throw new VectorDatabaseError(
        `Fetch failed: ${error instanceof Error ? error.message : String(error)}`,
        'fetch',
        error instanceof Error ? error : undefined
      );
    }
  }

  async delete(ids: string[]): Promise<void> {
    try {
      // Set namespace context for RLS
      await this.setNamespaceContext();

      console.log(
        `[PgVectorAdapter] Deleting ${ids.length} vectors from namespace '${this.namespace}'`
      );

      const { error } = await this.supabase
        .from(this.tableName)
        .delete()
        .in('id', ids)
        .eq('namespace', this.namespace);

      if (error) {
        throw new Error(`Failed to delete vectors: ${error.message}`);
      }

      console.log(
        `[PgVectorAdapter] Successfully deleted ${ids.length} vectors`
      );
    } catch (error) {
      console.error(
        `[PgVectorAdapter] Error deleting vectors from namespace '${this.namespace}':`,
        error
      );
      throw new VectorDatabaseError(
        `Delete failed: ${error instanceof Error ? error.message : String(error)}`,
        'delete',
        error instanceof Error ? error : undefined
      );
    }
  }

  async updateMetadata(
    id: string,
    metadata: Record<string, any>
  ): Promise<void> {
    try {
      // Set namespace context for RLS
      await this.setNamespaceContext();

      console.log(
        `[PgVectorAdapter] Updating metadata for vector '${id}' in namespace '${this.namespace}'`
      );

      const sanitizedMetadata = VectorUtils.sanitizeMetadata({
        ...metadata,
        namespace: this.namespace,
      });

      const { error } = await this.supabase
        .from(this.tableName)
        .update({
          metadata: sanitizedMetadata,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .eq('namespace', this.namespace);

      if (error) {
        throw new Error(`Failed to update metadata: ${error.message}`);
      }

      console.log(
        `[PgVectorAdapter] Successfully updated metadata for vector '${id}'`
      );
    } catch (error) {
      console.error(
        `[PgVectorAdapter] Error updating metadata for vector '${id}':`,
        error
      );
      throw new VectorDatabaseError(
        `Update metadata failed: ${error instanceof Error ? error.message : String(error)}`,
        'updateMetadata',
        error instanceof Error ? error : undefined
      );
    }
  }

  async getStats(): Promise<VectorDatabaseStats> {
    try {
      // Set namespace context for RLS
      await this.setNamespaceContext();

      console.log(
        `[PgVectorAdapter] Getting stats for namespace '${this.namespace}'`
      );

      // Get total count for this namespace
      const { count, error } = await this.supabase
        .from(this.tableName)
        .select('*', { count: 'exact', head: true })
        .eq('namespace', this.namespace);

      if (error) {
        throw new Error(`Failed to get stats: ${error.message}`);
      }

      // Get stats for all namespaces (for compatibility)
      const { data: allNamespaces, error: namespacesError } =
        await this.supabase
          .from(this.tableName)
          .select('namespace')
          .neq('namespace', null);

      if (namespacesError) {
        console.warn(
          `[PgVectorAdapter] Warning: Could not fetch all namespaces: ${namespacesError.message}`
        );
      }

      const namespaces: Record<string, { vectorCount: number }> = {};
      if (allNamespaces) {
        const namespaceCounts: Record<string, number> = {};
        allNamespaces.forEach(row => {
          namespaceCounts[row.namespace] =
            (namespaceCounts[row.namespace] || 0) + 1;
        });

        for (const [ns, count] of Object.entries(namespaceCounts)) {
          namespaces[ns] = { vectorCount: count };
        }
      }

      return {
        dimension: 1536, // Standard OpenAI embedding dimension
        indexFullness: count ? count / 1000000 : 0, // Rough estimate
        namespaces,
      };
    } catch (error) {
      console.error(`[PgVectorAdapter] Error getting stats:`, error);
      throw new VectorDatabaseError(
        `Get stats failed: ${error instanceof Error ? error.message : String(error)}`,
        'getStats',
        error instanceof Error ? error : undefined
      );
    }
  }

  async namespaceExists(): Promise<boolean> {
    try {
      // Set namespace context for RLS
      await this.setNamespaceContext();

      const { count, error } = await this.supabase
        .from(this.tableName)
        .select('*', { count: 'exact', head: true })
        .eq('namespace', this.namespace);

      if (error) {
        console.error(
          `[PgVectorAdapter] Error checking namespace existence:`,
          error
        );
        return false;
      }

      return (count || 0) > 0;
    } catch (error) {
      console.error(
        `[PgVectorAdapter] Error checking namespace existence:`,
        error
      );
      return false;
    }
  }
}
