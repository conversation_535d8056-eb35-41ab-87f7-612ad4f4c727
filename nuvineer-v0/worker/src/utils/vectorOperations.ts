/**
 * Vector Operations for Architectural Decisions
 *
 * This module provides high-level operations for storing, retrieving, and analyzing
 * architectural decisions using vector databases. It includes functionality for:
 * - Storing architectural decisions as vectors
 * - Performing RAG (Retrieval-Augmented Generation) queries
 * - Finding similar decisions and file overlaps
 * - Analyzing relationships between decisions
 */

import crypto from 'crypto';
import { VectorDatabase } from './vectorDatabase';
import { VectorDatabaseService } from './vectorDatabaseService';
import { logger } from './logger';
import { openaiClient } from './openaiClient';
import { getRepositoryNamespace } from './repositoryNamespace';
import { VectorSearchResult } from '../types/vector';
import {
  DecisionMetadata,
  GetAllDecisionMetadataOptions,
} from '../types/orchestrator';

/**
 * Get vector database instance for a repository
 */
async function getVectorDatabaseForRepository(
  installationId: string | number,
  repositorySlug: string
): Promise<VectorDatabase> {
  const numericInstallationId =
    typeof installationId === 'string'
      ? parseInt(installationId, 10)
      : installationId;
  return VectorDatabaseService.getVectorDatabase(
    numericInstallationId,
    repositorySlug
  );
}

/**
 * Validates namespace format and ensures it matches the expected repository
 */
function validateNamespace(
  installationId: number | string,
  repositorySlug: string,
  providedNamespace?: string
): string {
  const expectedNamespace = getRepositoryNamespace(
    installationId,
    repositorySlug
  );

  // If namespace was provided, validate it matches expected
  if (providedNamespace && providedNamespace !== expectedNamespace) {
    throw new Error(
      `Namespace mismatch: expected ${expectedNamespace}, got ${providedNamespace}`
    );
  }

  // Validate format
  if (!expectedNamespace.match(/^\d+-[a-z0-9-]+--[a-z0-9-]+$/)) {
    throw new Error(`Invalid namespace format: ${expectedNamespace}`);
  }

  return expectedNamespace;
}

/**
 * Extracts repository slug from namespace for validation
 */
function extractRepoSlugFromNamespace(namespace: string): string {
  const parts = namespace.split('-');
  if (parts.length < 3) {
    throw new Error(`Cannot parse namespace: ${namespace}`);
  }

  const ownerRepoPart = parts.slice(1).join('-');
  const match = ownerRepoPart.match(/^(.+)--(.+)$/);

  if (!match) {
    throw new Error(`Invalid namespace format: ${namespace}`);
  }

  return `${match[1]}/${match[2]}`;
}

/**
 * Helper function to validate repository slug format consistency
 */
function validateRepositorySlug(
  metadataRepoSlug: string,
  expectedRepoSlug: string,
  logPrefix: string,
  itemId?: string
): boolean {
  if (!metadataRepoSlug) {
    return true; // No metadata repository slug to validate
  }

  // Handle both formats: namespace format (66900160-jabubaker--jdeferred) and GitHub format (jabubaker/jdeferred)
  let normalizedMetadataSlug = metadataRepoSlug;

  // If it's in namespace format, extract the GitHub format
  if (metadataRepoSlug.includes('--')) {
    try {
      normalizedMetadataSlug = extractRepoSlugFromNamespace(metadataRepoSlug);
    } catch (error) {
      logger.error(
        `${logPrefix} Failed to extract repo slug from namespace format: ${metadataRepoSlug}`
      );
      return false;
    }
  }

  if (normalizedMetadataSlug !== expectedRepoSlug) {
    logger.error(
      `${logPrefix} DATA CONTAMINATION DETECTED: ${itemId ? `Item ${itemId}` : 'Item'} belongs to ${normalizedMetadataSlug}, expected ${expectedRepoSlug}`
    );
    return false;
  }

  return true;
}

/**
 * Validates data isolation to prevent contamination
 */
function validateDataIsolation(
  namespace: string,
  metadata: Record<string, any>,
  operation: string
): void {
  const expectedRepoSlug = extractRepoSlugFromNamespace(namespace);

  if (
    metadata.repository_slug &&
    metadata.repository_slug !== expectedRepoSlug
  ) {
    throw new Error(
      `Data contamination detected in ${operation}: metadata.repository_slug (${metadata.repository_slug}) doesn't match namespace repository (${expectedRepoSlug})`
    );
  }
  metadata.repository_slug = expectedRepoSlug;
}

/**
 * Store a decision record in the vector database
 */
export async function storeDecisionRecord(
  decisionRecord: any,
  installationId: number | string,
  repositorySlug: string
): Promise<string> {
  const namespace = validateNamespace(installationId, repositorySlug);
  const logPrefix = `[VectorOps Store - Repo: ${repositorySlug}]`;

  logger.info(`${logPrefix} Storing decision: "${decisionRecord.title}"`);

  try {
    // Get vector database instance for this repository
    const vectorDb = await getVectorDatabaseForRepository(
      installationId,
      repositorySlug
    );

    // Generate unique ID
    const prNumber = decisionRecord.pr_number || 'unknownPR';
    const timestamp = Date.now();
    const contentToHash = `${decisionRecord.title || ''}-${decisionRecord.description || ''}`;

    // Create proper hash using crypto
    const shortHash = crypto
      .createHash('sha256')
      .update(contentToHash)
      .digest('hex')
      .substring(0, 8);
    const vectorId = `decision_${prNumber}_${timestamp}_${shortHash}`;

    // Generate embedding for the decision content
    const textForEmbedding = `${decisionRecord.title}\n${decisionRecord.description}`;
    const embedding = await generateEmbedding(textForEmbedding);

    // Prepare metadata with validation
    const metadata = {
      ...decisionRecord,
      repository_slug: repositorySlug,
      is_superseded: false,
    };

    // Validate data isolation
    validateDataIsolation(namespace, metadata, 'storeDecisionRecord');

    // Store the vector with real embedding
    await vectorDb.upsert([
      {
        id: vectorId,
        values: embedding,
        metadata: metadata,
      },
    ]);

    logger.info(
      `${logPrefix} Successfully stored decision with ID: ${vectorId}`
    );
    return vectorId;
  } catch (error) {
    logger.error(`${logPrefix} Error storing decision:`, error);
    throw error;
  }
}

/**
 * Mark a decision as superseded
 */
export async function markDecisionAsSuperseded(
  supersededDecisionId: string,
  supersedingDecisionId: string,
  installationId: number | string,
  repositorySlug: string
): Promise<void> {
  const namespace = validateNamespace(installationId, repositorySlug);
  const logPrefix = `[VectorOps Supersede - Repo: ${repositorySlug} - ID: ${supersededDecisionId}]`;

  logger.info(`${logPrefix} Marking as superseded by ${supersedingDecisionId}`);

  try {
    // Get vector database instance for this repository
    const vectorDb = await getVectorDatabaseForRepository(
      installationId,
      repositorySlug
    );

    // Update metadata to mark as superseded
    await vectorDb.updateMetadata(supersededDecisionId, {
      is_superseded: true,
      superseded_by: supersedingDecisionId,
      superseded_at: new Date().toISOString(),
    });

    logger.info(`${logPrefix} Successfully marked as superseded`);
  } catch (error) {
    logger.error(`${logPrefix} Error marking decision as superseded:`, error);
    throw error;
  }
}

/**
 * Generate embedding using OpenAI
 */
export async function generateEmbedding(
  text: string,
  model = 'text-embedding-3-small'
): Promise<number[]> {
  try {
    const response = await openaiClient.embeddings.create({
      model: model,
      input: text.replace(/\n/g, ' '),
      encoding_format: 'float',
    });
    return response.data[0].embedding;
  } catch (error) {
    logger.error('[VectorOps] Error generating OpenAI embedding:', error);
    throw error;
  }
}

/**
 * Query vector database
 */
export async function queryVectorDatabase(
  queryText: string,
  installationId: number | string,
  repositorySlug: string,
  options: any = {}
): Promise<VectorSearchResult[]> {
  const namespace = validateNamespace(installationId, repositorySlug);
  const { topK, k, minScore, filter = {} } = options;
  // Support both 'k' and 'topK' parameter names for backward compatibility
  const finalTopK = topK || k || 5;
  const finalMinScore = minScore !== undefined ? minScore : 0.7;
  const logPrefix = `[VectorOps Query - Repo: ${repositorySlug}]`;

  logger.debug(
    `${logPrefix} Query params: topK=${finalTopK}, minScore=${finalMinScore}`
  );

  try {
    const vectorDb = await getVectorDatabaseForRepository(
      installationId,
      repositorySlug
    );

    // Generate embedding for query
    const queryEmbedding = await generateEmbedding(queryText);

    // Query the vector database
    const results = await vectorDb.query({
      vector: queryEmbedding,
      topK: finalTopK,
      minScore: finalMinScore,
      filter,
      includeMetadata: true,
      includeValues: false,
    });

    logger.debug(`${logPrefix} Found ${results.length} results`);
    return results;
  } catch (error) {
    logger.error(`${logPrefix} Error querying vector database:`, error);
    return [];
  }
}

/**
 * Fetch vectors by IDs
 */
export async function fetchVectorsByIds(
  vectorIds: string[],
  installationId: number | string,
  repositorySlug: string
): Promise<any[]> {
  const namespace = validateNamespace(installationId, repositorySlug);
  const logPrefix = `[VectorOps Fetch - Repo: ${repositorySlug}]`;

  try {
    const vectorDb = await getVectorDatabaseForRepository(
      installationId,
      repositorySlug
    );
    const records = await vectorDb.fetch(vectorIds);

    // Convert records object into an array and validate repository isolation
    const results = [];
    for (const id of vectorIds) {
      const record = records[id];
      if (record && record.metadata) {
        // Validate repository isolation
        if (
          validateRepositorySlug(
            record.metadata.repository_slug,
            repositorySlug,
            logPrefix,
            id
          )
        ) {
          results.push(record);
        }
      }
    }

    logger.debug(
      `${logPrefix} Fetched ${results.length} valid vectors out of ${vectorIds.length} requested`
    );
    return results;
  } catch (error) {
    logger.error(`${logPrefix} Error fetching vectors:`, error);
    return [];
  }
}

/**
 * Get vector database statistics
 */
export async function getVectorDatabaseStats(
  installationId: number | string,
  repositorySlug: string
): Promise<any> {
  const logPrefix = `[VectorOps Stats - Repo: ${repositorySlug}]`;

  try {
    const vectorDb = await getVectorDatabaseForRepository(
      installationId,
      repositorySlug
    );
    const stats = await vectorDb.getStats();

    logger.debug(`${logPrefix} Retrieved stats:`, stats);
    return stats;
  } catch (error) {
    logger.error(`${logPrefix} Error getting stats:`, error);
    return { totalVectorCount: 0, dimension: 1536, namespaces: {} };
  }
}

/**
 * Get all decision metadata for a repository
 */
export async function getAllDecisionMetadataForRepo(
  installationId: number | string,
  repositorySlug: string,
  options: GetAllDecisionMetadataOptions = {}
): Promise<DecisionMetadata[]> {
  const namespace = validateNamespace(installationId, repositorySlug);
  const { includeSuperseded = false } = options;
  const logPrefix = `[VectorOps GetAll - Repo: ${repositorySlug}]`;

  logger.debug(
    `${logPrefix} Fetching all decisions. Include Superseded: ${includeSuperseded}`
  );

  try {
    const vectorDb = await getVectorDatabaseForRepository(
      installationId,
      repositorySlug
    );

    // Query for all decisions in this namespace
    // Create a broad query vector (average of common terms)
    const queryText = 'architectural decision software design';
    const queryEmbedding = await generateEmbedding(queryText);

    const filter = includeSuperseded ? {} : { is_superseded: false };

    const results = await vectorDb.query({
      vector: queryEmbedding,
      topK: 1000, // Large number to get all decisions
      minScore: 0.0, // Very low threshold to include all
      filter,
      includeMetadata: true,
      includeValues: false,
    });

    // Convert to DecisionMetadata format
    const decisions: DecisionMetadata[] = results.map(result => ({
      id: result.id,
      title: result.metadata.title || 'Unknown Title',
      status: result.metadata.status || 'active',
      decision_date:
        result.metadata.decision_date ||
        result.metadata.extracted_at ||
        new Date().toISOString(),
      decision_outcome: result.metadata.decision_outcome,
      context: result.metadata.context || result.metadata.description,
      consequences: result.metadata.consequences,
      related_files: Array.isArray(result.metadata.related_files)
        ? result.metadata.related_files
        : typeof result.metadata.related_files === 'string'
          ? result.metadata.related_files.split(', ')
          : [],
      risks: Array.isArray(result.metadata.risks)
        ? result.metadata.risks
        : typeof result.metadata.risks === 'string'
          ? JSON.parse(result.metadata.risks || '[]')
          : [],
      alternatives: result.metadata.alternatives,
      superseded_by: result.metadata.superseded_by,
      supersedes: result.metadata.supersedes,
      file_path: result.metadata.file_path,
      created_at: result.metadata.extracted_at,
      updated_at: result.metadata.updated_at,
    }));

    logger.debug(
      `${logPrefix} Retrieved ${decisions.length} decisions from vector database`
    );
    return decisions;
  } catch (error) {
    logger.error(`${logPrefix} Error fetching decision metadata:`, error);
    return [];
  }
}

/**
 * Get vector by ID
 */
export async function getVectorById(
  id: string,
  installationId: number | string,
  repositorySlug: string
): Promise<any | null> {
  const logPrefix = `[VectorOps GetById - Repo: ${repositorySlug} - ID: ${id}]`;

  try {
    const vectorDb = await getVectorDatabaseForRepository(
      installationId,
      repositorySlug
    );
    const records = await vectorDb.fetch([id]);

    return records[id] || null;
  } catch (error) {
    logger.error(`${logPrefix} Error fetching vector:`, error);
    return null;
  }
}

/**
 * Get vectors by IDs
 */
export async function getVectorsByIds(
  ids: string[],
  installationId: number | string,
  repositorySlug: string
): Promise<any[]> {
  if (!ids || ids.length === 0) {
    return [];
  }

  const logPrefix = `[VectorOps GetByIds - Repo: ${repositorySlug}]`;

  try {
    const vectorDb = await getVectorDatabaseForRepository(
      installationId,
      repositorySlug
    );
    const records = await vectorDb.fetch(ids);

    // Convert records object into an array
    return ids.map(id => records[id]).filter(record => record !== undefined);
  } catch (error) {
    logger.error(`${logPrefix} Error fetching vectors by IDs:`, error);
    return [];
  }
}

/**
 * Upsert vectors directly
 */
export async function upsertVectors(
  vectors: Array<{
    id: string;
    values: number[];
    metadata: Record<string, any>;
  }>,
  installationId: number | string,
  repositorySlug: string
): Promise<void> {
  const namespace = validateNamespace(installationId, repositorySlug);
  const logPrefix = `[VectorOps Upsert - Repo: ${repositorySlug}]`;

  try {
    const vectorDb = await getVectorDatabaseForRepository(
      installationId,
      repositorySlug
    );
    await vectorDb.upsert(vectors);
    logger.info(`${logPrefix} Successfully upserted ${vectors.length} vectors`);
  } catch (error) {
    logger.error(`${logPrefix} Error upserting vectors:`, error);
    throw error;
  }
}

/**
 * Update vector metadata
 */
export async function updateVectorMetadata(
  id: string,
  metadata: Record<string, any>,
  installationId: number | string,
  repositorySlug: string
): Promise<void> {
  const namespace = validateNamespace(installationId, repositorySlug);
  const logPrefix = `[VectorOps UpdateMetadata - Repo: ${repositorySlug} - ID: ${id}]`;

  try {
    const vectorDb = await getVectorDatabaseForRepository(
      installationId,
      repositorySlug
    );
    await vectorDb.updateMetadata(id, metadata);
    logger.info(`${logPrefix} Successfully updated metadata`);
  } catch (error) {
    logger.error(`${logPrefix} Error updating metadata:`, error);
    throw error;
  }
}

/**
 * Query RAG for decision context
 */
export async function queryRAG(
  decision: any,
  installationId: number | string,
  repositorySlug: string,
  k = 3,
  options: any = {}
): Promise<any[]> {
  const namespace = validateNamespace(installationId, repositorySlug);
  const { minScore = 0.7, filter = null } = options;
  const queryText = `${decision.title}\n${decision.description}`;
  const logPrefix = `[VectorOps RAG - Repo: ${repositorySlug} - Decision: "${decision.title}"]`;

  logger.info(
    `${logPrefix} Starting RAG query. Params: k=${k}, minScore=${minScore}`
  );

  try {
    const vectorDb = await getVectorDatabaseForRepository(
      installationId,
      repositorySlug
    );

    // Generate embedding for the decision
    const queryEmbedding = await generateEmbedding(queryText);

    // Apply default filter for non-superseded decisions
    let finalFilter = filter;
    if (!filter) {
      finalFilter = { is_superseded: false };
    }

    const results = await vectorDb.query({
      vector: queryEmbedding,
      topK: k,
      minScore,
      filter: finalFilter,
      includeMetadata: true,
      includeValues: false,
    });

    // Filter out self-references if the decision has an ID
    const filteredResults = decision.id
      ? results.filter(result => result.id !== decision.id)
      : results;

    logger.info(
      `${logPrefix} Found ${filteredResults.length} relevant decisions for RAG context`
    );
    return filteredResults;
  } catch (error) {
    logger.error(`${logPrefix} Error in RAG query:`, error);
    return [];
  }
}

/**
 * Find similar decisions
 */
export async function findSimilarDecisions(
  decisionId: string,
  installationId: number | string,
  repositorySlug: string,
  k = 5,
  minScore = 0.7
): Promise<any[]> {
  const namespace = validateNamespace(installationId, repositorySlug);
  const logPrefix = `[VectorOps Similar - Repo: ${repositorySlug} - ID: ${decisionId}]`;

  logger.info(
    `${logPrefix} Finding top ${k} similar decisions (minScore: ${minScore})`
  );

  try {
    const vectorDb = await getVectorDatabaseForRepository(
      installationId,
      repositorySlug
    );

    // First, get the target decision
    const targetDecision = await vectorDb.fetch([decisionId]);
    if (!targetDecision[decisionId]) {
      logger.warn(`${logPrefix} Target decision not found`);
      return [];
    }

    const target = targetDecision[decisionId];
    if (!target.values || target.values.length === 0) {
      // If no embedding, generate one from metadata
      const textContent = `${target.metadata?.title || ''}\n${target.metadata?.description || ''}`;
      target.values = await generateEmbedding(textContent);
    }

    // Query for similar decisions
    const results = await vectorDb.query({
      vector: target.values,
      topK: k + 1, // +1 to account for self-match
      minScore,
      filter: { is_superseded: false },
      includeMetadata: true,
      includeValues: false,
    });

    // Filter out the original decision
    const similarDecisions = results.filter(result => result.id !== decisionId);

    logger.info(
      `${logPrefix} Found ${similarDecisions.length} similar decisions`
    );
    return similarDecisions.slice(0, k); // Ensure we don't return more than requested
  } catch (error) {
    logger.error(`${logPrefix} Error finding similar decisions:`, error);
    return [];
  }
}

/**
 * Find decisions by file overlap
 */
export async function findDecisionsByFileOverlap(
  newDecision: any,
  installationId: number | string,
  repositorySlug: string
): Promise<any[]> {
  const namespace = validateNamespace(installationId, repositorySlug);
  const logPrefix = `[VectorOps FileOverlap - Repo: ${repositorySlug}]`;
  const relatedFiles = newDecision.related_files || [];

  if (relatedFiles.length === 0) {
    logger.debug(`${logPrefix} No related files provided`);
    return [];
  }

  logger.info(
    `${logPrefix} Searching for decisions related to files: ${relatedFiles.join(', ')}`
  );

  try {
    const vectorDb = await getVectorDatabaseForRepository(
      installationId,
      repositorySlug
    );

    // Create a query based on the file names
    const fileQuery = relatedFiles.join(' ');
    const queryEmbedding = await generateEmbedding(fileQuery);

    const results = await vectorDb.query({
      vector: queryEmbedding,
      topK: 20,
      minScore: 0.5, // Lower threshold for file overlap
      filter: { is_superseded: false },
      includeMetadata: true,
      includeValues: false,
    });

    // Filter for decisions that actually have overlapping files
    const overlappingDecisions = results.filter(result => {
      const decisionFiles = result.metadata.related_files;
      if (!decisionFiles) return false;

      // Parse files if they're stored as a string
      const fileList =
        typeof decisionFiles === 'string'
          ? decisionFiles.split(', ')
          : decisionFiles;

      // Check for file overlap
      return relatedFiles.some((file: string) =>
        fileList.some(
          (decisionFile: string) =>
            decisionFile.includes(file) || file.includes(decisionFile)
        )
      );
    });

    logger.info(
      `${logPrefix} Found ${overlappingDecisions.length} decisions with file overlap`
    );
    return overlappingDecisions;
  } catch (error) {
    logger.error(
      `${logPrefix} Error finding decisions by file overlap:`,
      error
    );
    return [];
  }
}

/**
 * Find decisions by domain concepts
 */
export async function findDecisionsByDomainConcepts(
  newDecision: any,
  installationId: number | string,
  repositorySlug: string
): Promise<any[]> {
  const namespace = validateNamespace(installationId, repositorySlug);
  const logPrefix = `[VectorOps DomainConcepts - Repo: ${repositorySlug}]`;
  const domainConcepts = newDecision.domain_concepts;

  if (!Array.isArray(domainConcepts) || domainConcepts.length === 0) {
    logger.debug(`${logPrefix} No domain concepts provided`);
    return [];
  }

  logger.info(
    `${logPrefix} Searching for decisions related to concepts: ${domainConcepts.join(', ')}`
  );

  try {
    const vectorDb = await getVectorDatabaseForRepository(
      installationId,
      repositorySlug
    );

    // Create a query based on domain concepts
    const conceptQuery = domainConcepts.join(' ');
    const queryEmbedding = await generateEmbedding(conceptQuery);

    const results = await vectorDb.query({
      vector: queryEmbedding,
      topK: 20,
      minScore: 0.6,
      filter: { is_superseded: false },
      includeMetadata: true,
      includeValues: false,
    });

    logger.info(
      `${logPrefix} Found ${results.length} decisions related to domain concepts`
    );
    return results;
  } catch (error) {
    logger.error(
      `${logPrefix} Error finding decisions by domain concepts:`,
      error
    );
    return [];
  }
}

/**
 * Find decisions by impact overlap
 */
export async function findDecisionsByImpactOverlap(
  newDecision: any,
  installationId: number | string,
  repositorySlug: string
): Promise<any[]> {
  const namespace = validateNamespace(installationId, repositorySlug);
  const logPrefix = `[VectorOps ImpactOverlap - Repo: ${repositorySlug}]`;
  const implications = newDecision.implications || '';

  if (!implications.trim()) {
    logger.debug(`${logPrefix} No implications provided`);
    return [];
  }

  logger.info(
    `${logPrefix} Searching for decisions with overlapping implications`
  );

  try {
    const vectorDb = await getVectorDatabaseForRepository(
      installationId,
      repositorySlug
    );

    // Generate embedding for the implications
    const queryEmbedding = await generateEmbedding(implications);

    const results = await vectorDb.query({
      vector: queryEmbedding,
      topK: 15,
      minScore: 0.7,
      filter: { is_superseded: false },
      includeMetadata: true,
      includeValues: false,
    });

    logger.info(
      `${logPrefix} Found ${results.length} decisions with impact overlap`
    );
    return results;
  } catch (error) {
    logger.error(
      `${logPrefix} Error finding decisions by impact overlap:`,
      error
    );
    return [];
  }
}
