/**
 * Checks if a filename/path typically represents non-architectural code.
 * Keeps package.json as it's often relevant.
 * @param {string} filename - The filename or path.
 * @returns {boolean} - True if the file content should likely be skipped for architectural analysis.
 */
export function shouldSkipFileContent(filename: string): boolean {
  if (!filename) return true; // Skip if no filename

  // Keep package.json
  if (filename.endsWith('package.json')) {
    return false;
  }

  const lowerFilename = filename.toLowerCase();
  const extension = lowerFilename.split('.').pop() || '';
  const name = lowerFilename.split('/').pop(); // Get just the filename

  // Common extensions to skip
  const skipExtensions = [
    'md',
    'txt',
    'rst',
    'jpg',
    'jpeg',
    'png',
    'gif',
    'svg',
    'ico',
    'yml',
    'yaml', // Often config, can be relevant but often not for direct arch decisions
    'json', // General JSO<PERSON>, exclude package.json handled above
    'lock', // e.g., package-lock.json, yarn.lock
    'example',
    'template',
    'log',
    'csv',
    'tsv',
    'xml',
    'pdf',
    'doc',
    'docx',
  ];

  // Common filenames/paths to skip
  const skipNamesOrPaths = [
    'license',
    'readme', // Base README already checked by extension
    'contributing',
    'codeowners',
    'changelog',
    'dockerfile', // Can be relevant, but often deployment detail
    '.gitignore',
    '.dockerignore',
    '.npmignore',
    '.gitattributes',
    '.editorconfig',
    // Config files (often checked by yaml/json extension too)
    'jest.config.',
    'vite.config.',
    'tsconfig.',
    'babel.config.',
    // CI/CD pipelines
    '.github/',
    '.gitlab-ci.yml',
  ];

  if (skipExtensions.includes(extension)) {
    return true;
  }

  if (skipNamesOrPaths.some(skip => lowerFilename.includes(skip))) {
    return true;
  }

  return false;
}
