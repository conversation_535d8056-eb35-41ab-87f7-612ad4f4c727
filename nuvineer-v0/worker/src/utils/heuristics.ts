/**
 * This file contains various heuristics used throughout the application to make
 * automated decisions, such as filtering commits or prioritizing analysis.
 */

/**
 * A conservative filter to identify commits that are almost certainly not architectural.
 * This check is designed for high reliability to avoid filtering out significant commits.
 * It does NOT use file paths to ensure it is scalable and maintainable across repositories.
 * @param commitMessage - The commit message or PR title.
 * @returns True if the commit is almost certainly a maintenance/non-architectural commit.
 */
export function isDefinitelyNotArchitectural(commitMessage: string): boolean {
  if (!commitMessage) {
    return false;
  }
  const lowerCaseMessage = commitMessage.toLowerCase();

  // List of prefixes for conventional commits that are reliably non-architectural.
  const nonArchPrefixes = [
    'chore:',
    'docs:',
    'fix(docs):',
    'style:',
    'ci:',
    'test:',
    'build:',
    'refactor(docs):',
  ];
  if (nonArchPrefixes.some(prefix => lowerCaseMessage.startsWith(prefix))) {
    return true;
  }

  // List of keywords that are reliably non-architectural.
  const nonArchKeywords = [
    'translation',
    'localization',
    'i18n',
    'l10n',
    'crowdin',
    'typo',
    'lint',
    'formatting',
    'update snapshot',
  ];
  if (nonArchKeywords.some(keyword => lowerCaseMessage.includes(keyword))) {
    return true;
  }

  return false;
}
