/**
 * Shared Anthropic client for the worker
 */
import { Anthropic } from '@anthropic-ai/sdk';
import { logger } from './logger';

const anthropicApiKey = process.env.ANTHROPIC_API_KEY;

if (!anthropicApiKey) {
  logger.error('ANTHROPIC_API_KEY not configured for anthropicClient');
  throw new Error('ANTHROPIC_API_KEY not configured for anthropicClient');
}

// Create shared Anthropic client instance
export const anthropicClient = new Anthropic({
  apiKey: anthropicApiKey,
});

logger.info('Anthropic client initialized successfully');
