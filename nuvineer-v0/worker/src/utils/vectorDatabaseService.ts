// Vector Database Service

import { VectorDatabase, VectorDatabaseFactory } from './vectorDatabase';
import { VectorDatabaseType } from '../types/vector';
import { getRepositoryNamespace } from './repositoryNamespace';
import { getRepositorySettings } from './repositorySettingsService';
import { logger } from './logger';

export class VectorDatabaseService {
  private static instanceCache: Map<string, VectorDatabase> = new Map();

  static async getVectorDatabase(
    installationId: number | string,
    repositorySlug: string
  ): Promise<VectorDatabase> {
    const namespace = getRepositoryNamespace(installationId, repositorySlug);
    const cacheKey = `${namespace}`;

    if (this.instanceCache.has(cacheKey)) {
      return this.instanceCache.get(cacheKey)!;
    }

    const vectorDbType = await this.getVectorDatabaseType(repositorySlug);

    logger.info(
      `[VectorDatabaseService] Creating ${vectorDbType} instance for repository ${repositorySlug} (namespace: ${namespace})`
    );

    const vectorDb = await VectorDatabaseFactory.create({
      type: vectorDbType,
      namespace,
    });

    this.instanceCache.set(cacheKey, vectorDb);
    return vectorDb;
  }

  private static async getVectorDatabaseType(
    repositorySlug: string
  ): Promise<VectorDatabaseType> {
    try {
      const { data: settings } = await getRepositorySettings(repositorySlug);

      if (settings?.vector_db) {
        const cleanVectorDb = String(settings.vector_db)
          .replace(/::text$/, '')
          .trim();

        if (cleanVectorDb === 'pinecone' || cleanVectorDb === 'pgvector') {
          return cleanVectorDb as VectorDatabaseType;
        }

        logger.warn(
          `[VectorDatabaseService] Invalid vector_db value '${settings.vector_db}' for ${repositorySlug}, defaulting to pgvector`
        );
      }

      logger.debug(
        `[VectorDatabaseService] No vector_db setting found for ${repositorySlug}, defaulting to pgvector`
      );
      return 'pgvector';
    } catch (error) {
      logger.warn(
        `[VectorDatabaseService] Error fetching repository settings for ${repositorySlug}, defaulting to pgvector:`,
        error
      );
      return 'pgvector';
    }
  }

  static clearCache(
    installationId: number | string,
    repositorySlug: string
  ): void {
    const namespace = getRepositoryNamespace(installationId, repositorySlug);
    const cacheKey = `${namespace}`;
    this.instanceCache.delete(cacheKey);
  }

  static clearAllCache(): void {
    this.instanceCache.clear();
  }
}

export async function getVectorDatabaseForRepository(
  installationId: number | string,
  repositorySlug: string
): Promise<VectorDatabase> {
  return VectorDatabaseService.getVectorDatabase(
    installationId,
    repositorySlug
  );
}
