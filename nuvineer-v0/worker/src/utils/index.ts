// Shared utilities and imports for the server endpoints
import config from '../config';

export { getOctokit, getAuthToken, checkRateLimit } from './github-auth';

export {
  getRecentCommits,
  getCommitDetail,
  createPseudoPrFromCommit,
  getRecentPRs,
  getPR,
  getPRFiles,
  getPRComments,
} from './github';

export {
  callLightweightLlmForSignificance,
  callClaudeLlmForJson,
  callClaudeLlmRaw,
} from './llmUtils';

export {
  storeDecisionRecord,
  markDecisionAsSuperseded,
  generateEmbedding,
  queryVectorDatabase,
  fetchVectorsByIds,
  getVectorDatabaseStats,
  getVectorById,
  getVectorsByIds,
  upsertVectors,
  updateVectorMetadata,
  queryRAG,
  findSimilarDecisions,
  findDecisionsByFileOverlap,
  findDecisionsByDomainConcepts,
  findDecisionsByImpactOverlap,
} from './vectorOperations';

export { getVectorDatabaseForRepository } from './vectorDatabaseService';

export {
  VectorDatabase,
  VectorDatabaseFactory,
  VectorDatabaseError,
  VectorNotFoundError,
  NamespaceNotFoundError,
  VectorUtils,
} from './vectorDatabase';

export { getRepositoryNamespace } from './repositoryNamespace';

export { isDefinitelyNotArchitectural } from './heuristics';

export { logger } from './logger';

export {
  getRepositorySettings,
  createRepositorySettings,
  updateRepositorySettings,
  upsertRepositorySettings,
  deleteRepositorySettings,
  getAllRepositorySettings,
} from './repositorySettingsService';

export {
  processMergedPR,
  getAllDecisionMetadataForRepo,
  extractParamsFromNamespace,
  validateGitHubSignature,
  logProcessedPR,
} from './orchestratorUtils';

// Helper function to validate cron secret
export function validateCronSecret(req: any): boolean {
  if (config.cronSecret) {
    const providedSecret =
      req.headers['authorization']?.split(' ')[1] || req.query.secret;
    return providedSecret === config.cronSecret;
  }
  return true; // No secret required
}

// Re-export shared Supabase client
export { supabaseAdmin } from './supabaseClient';

// Re-export shared OpenAI client
export { openaiClient } from './openaiClient';

// Re-export shared Pinecone client
export { pineconeClient } from './pineconeClient';

// Re-export shared Anthropic client
export { anthropicClient } from './anthropicClient';
