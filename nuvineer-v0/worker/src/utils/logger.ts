import chalk from 'chalk';
import util from 'util';

const isDebugMode = process.env.DEBUG === 'true';

/**
 * Format an object for logging
 * @param obj - Object to format
 * @returns Formatted string
 */
function formatObject(obj: any): string {
  if (obj === undefined) return 'undefined';
  if (obj === null) return 'null';

  if (typeof obj === 'object') {
    try {
      // Try to stringify with indentation for readability
      return util.inspect(obj, { depth: 3, colors: true });
    } catch (error) {
      return String(obj);
    }
  }

  return String(obj);
}

/**
 * Enhanced logger utility for worker
 */
export const logger = {
  /**
   * Log info message
   * @param message - Message to log
   * @param data - Optional data to log
   */
  info: (message: string, data?: any): void => {
    console.log(chalk.blue('ℹ️ '), message);
    if (data !== undefined) {
      console.log(formatObject(data));
    }
  },

  /**
   * Log success message
   * @param message - Message to log
   * @param data - Optional data to log
   */
  success: (message: string, data?: any): void => {
    console.log(chalk.green('✓ '), message);
    if (data !== undefined) {
      console.log(formatObject(data));
    }
  },

  /**
   * Log warning message
   * @param message - Message to log
   * @param data - Optional data to log
   */
  warn: (message: string, data?: any): void => {
    console.log(chalk.yellow('⚠️ '), message);
    if (data !== undefined) {
      console.log(formatObject(data));
    }
  },

  /**
   * Log error message
   * @param message - Message to log
   * @param data - Optional data to log
   */
  error: (message: string, data?: any): void => {
    console.error(chalk.red('✗ '), message);
    if (data !== undefined) {
      // For errors, we want to see the stack trace if available
      if (data instanceof Error) {
        console.error(chalk.red(data.stack || data.message));
      } else {
        console.error(formatObject(data));
      }
    }
  },

  /**
   * Log debug message (only in debug mode)
   * @param message - Message to log
   * @param data - Optional data to log
   */
  debug: (message: string, data?: any): void => {
    if (isDebugMode) {
      console.log(chalk.gray('🔍 '), chalk.gray(message));
      if (data !== undefined) {
        console.log(chalk.gray(formatObject(data)));
      }
    }
  },
};
