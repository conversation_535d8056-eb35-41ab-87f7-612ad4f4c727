import { CodeChange, PR<PERSON>ontext, Comment, Decision } from 'src/types';
import { shouldSkipFileContent } from './analysisUtils';

/**
 * Generate a prompt for <PERSON> to extract architectural knowledge
 * @param {Object} prContext - PR context
 * @param {Array} codeChanges - Code changes extracted from diff
 * @param {Array} comments - PR comments
 * @param {Object} [deploymentConstitution] - Optional: The deployment constitution for the repository.
 * @returns {string} - Prompt for Claude
 */
export function generatePrompt(
  prContext: PRContext,
  codeChanges: CodeChange[],
  comments: Comment[],
  deploymentConstitution: any
) {
  // --- Filter Files ---
  const allFiles = codeChanges || [];
  const relevantFiles: CodeChange[] = [];
  const skippedFileInfos: string[] = []; // Store info about skipped files

  allFiles.forEach(file => {
    if (shouldSkipFileContent(file.filename)) {
      skippedFileInfos.push(
        `- ${file.filename} (${file.additions} additions, ${file.deletions} deletions) [Content Skipped]`
      );
    } else {
      relevantFiles.push(file);
    }
  });

  const relevantFileDetails = relevantFiles
    .map(
      file =>
        `- ${file.filename} (${file.additions} additions, ${file.deletions} deletions)`
    )
    .join('\n');
  const fileCount = allFiles.length;
  const relevantFileCount = relevantFiles.length;
  const skippedFileCount = skippedFileInfos.length;

  // Combine file details, putting relevant first
  let fileDetails = `Relevant Files Analyzed (${relevantFileCount}):\n${relevantFileDetails || 'None'}\n`;
  if (skippedFileCount > 0) {
    fileDetails += `\nFiles Skipped for Content Analysis (${skippedFileCount}):\n${skippedFileInfos.join('\n')}`;
  }
  // --- End Filter Files ---

  // Ensure comments is an array
  const validComments = Array.isArray(comments) ? comments : [];
  const commentDetails =
    validComments.length > 0
      ? validComments
          .map(
            comment =>
              `${comment.user?.login || 'Unknown User'}: ${comment.body}`
          )
          .join('\n\n')
      : 'No relevant comments found.';

  // --- ADDED: Format Deployment Constitution ---
  let deploymentContextSection = '';
  if (deploymentConstitution) {
    const formattedContext = formatDeploymentConstitution(
      deploymentConstitution
    );
    if (formattedContext) {
      deploymentContextSection = `\n**🚀 DEPLOYMENT CONTEXT:**\n${formattedContext}\n`;
    }
  }
  // --- END ADDED ---

  return `You are an expert software architect extracting SIGNIFICANT ARCHITECTURAL DECISIONS from code changes. Your task is to identify **only** architectural decisions that have long-term implications for the system's structure, behavior, evolution, and quality attributes.

**DO NOT simply summarize what the changes do functionally.** Your focus MUST be on the **architectural significance** and **long-term impact**. Avoid detailing routine refactorings or minor implementation details unless they represent a shift in architectural approach.

Based on the code changes below, critically evaluate how these changes impact the system's architecture. Focus *exclusively* on identifying and explaining:

1.  **STRUCTURAL IMPACTS:** How the changes modify components, services, layers, or their relationships in a non-trivial way. (e.g., introducing a new service, changing communication patterns between layers).
2.  **ARCHITECTURAL PATTERNS:** What significant patterns (e.g., CQRS, Event Sourcing, Microservices, Repository, Strategy) are being introduced, modified, or potentially violated?
3.  **QUALITY ATTRIBUTES:** How the changes materially affect key quality attributes like scalability, maintainability, security, performance, reliability, testability? (Be specific - *how* is scalability improved/hindered?).
4.  **TECHNICAL CONSTRAINTS:** What new architectural constraints are introduced or removed? (e.g., requiring use of a specific message queue, enforcing a specific data validation library).
5.  **CROSS-CUTTING CONCERNS:** How the changes significantly alter system-wide aspects like logging, authentication, monitoring, configuration management, etc.?
6.  **TECHNICAL DEBT:** Does the change introduce significant architectural debt (e.g., shortcuts impacting maintainability), or pay down existing structural debt?
7.  **FUTURE FLEXIBILITY:** How do the changes enable or materially hinder future architectural evolution or adoption of new technologies/approaches?
8.  **RATIONALE (Why this path?):** Explain the apparent reasons for choosing this specific architectural approach *now*, based *directly* on the PR context, code changes, and stated goals. Focus on the concrete advantages, constraints, or requirements addressed by this choice. Avoid speculation about underlying intent. (Detailed comparison with alternatives will be handled separately).

**PR Information:**
Title: ${prContext.title}
Description: ${prContext.body || 'No description provided'}
URL: ${prContext.html_url}

${deploymentContextSection}

**Files Changed (${fileCount}):**
${fileDetails}

**Code Changes (from relevant files):**
${formatCodeChanges(codeChanges.filter(change => !shouldSkipFileContent(change.filename)))}

**Key Comments:**
${commentDetails}

---
**CRITICAL REMINDER: Do NOT merely describe the features added or bugs fixed in this PR. Focus exclusively on the *architectural* choices and their long-term consequences.**
**Note:** Content/diffs for documentation, configuration, build files, lock files, and image files have been omitted from the 'Code Changes' section to focus on potential architectural impacts (unless it was package.json).
---
**Output Format:**

**General Writing Style:** For all descriptive fields ('title', 'description', 'rationale', 'implications', 'no_architecture_impact_reason'), write clear, concise, and high-signal content suitable for our knowledge base (used by humans of all levels and AI agents). Prioritize conveying the essential architectural insights without lengthy prose/excessive jargon/long sentences. Maintain analytical depth but use accessible language and minimal words/bullet points. Output *must* be easily understandable and parseable.

Format your response as a JSON object adhering strictly to this structure:
{
  "architectural_decisions": [
    {
      "title": "Concise, architecture-focused title capturing the core decision (e.g., 'Introduce Command Pattern for User Actions', 'Decouple Billing Service via Event Bus', 'Adopt Repository Pattern for Data Access')",
      "description": "Detailed analysis of the architectural change itself, focusing ONLY on structure, patterns, and principles. Avoid implementation details.",
      "rationale": "State the observable architectural reasoning for this path based on the PR context (code, description, comments). Explain the specific requirements, constraints, or goals addressed. Avoid speculative language like 'likely' or 'suggests'; focus on evidence present in the context.",
      "implications": "Analysis of long-term architectural impact: effects on specific quality attributes (scalability, maintainability, etc.), technical debt (introduced/reduced), and future flexibility (opportunities created/constraints imposed).",
      "confidence_score": "float (0.0 to 1.0) - Your confidence that this specific item truly represents a *significant* architectural decision based on the criteria provided. 1.0 = High confidence, 0.0 = Low confidence.",
      "related_files": [
        "src/path/to/relevant/file1.js",
        "src/path/to/another/file.ts"
      ],
     "domain_concepts": [
      "Category",
      "Subcategory", 
      "Pattern",
      "Specific"
      ], // Hierarchical array (max 4 levels) from broad architectural categories to specific implementation patterns (e.g., ['UI Patterns', 'Navigation', 'Space-Constrained Display', 'Hierarchical Data'] or ['Data Patterns', 'State Management', 'SWR Integration']). Enables concept drill-down from high-level to specific.
      "dev_prompt": "Concise, action-oriented guidance for developers working in this area (e.g., 'Creation flows: auto-navigate to newly created resource', 'Deep hierarchies: use ellipsis dropdown for space constraints'). Focus on what to do, not what was done.",
      "is_extension": "boolean - Set to true if this decision primarily extends or applies a previously established architectural pattern or principle to a new scope/context. Set to false if it introduces a fundamentally new pattern or approach.",
      "follows_standard_practice": "boolean - Set to true if this decision is implementing a well-established industry standard practice, design pattern, or architectural approach that is widely recognized as a best practice. For these decisions, alternatives analysis may be unnecessary as the approach is already validated by industry consensus.",
      "follows_standard_practice_reason": "string - **ONLY include this field IF \`follows_standard_practice\` is true.** Pattern name and key implementing file (e.g., 'Breadcrumb navigation with overflow: components/datarooms/dataroom-breadcrumb.tsx', 'SWR data fetching: components/visitors/dataroom-visitor-custom-fields.tsx'). Keep concise.",
      "data_model_changes": [
        "string - OPTIONAL. Include ONLY IF the PR introduces significant data model/schema changes. Describe each change concisely. Format for entities: 'entity_name (field1:type, field2:type, ...) - created | modified | deleted'. Format for enums: 'enum_name (enum: VALUE1, VALUE2) - created | modified | deleted'. Ensure 100% coverage. Examples: 'Users (email:string, preferences:json) - modified', 'NewTable (id:uuid, name:string) - created', 'OrderStatus (enum: PENDING, SHIPPED, DELIVERED) - created'."
      ], // This field is OPTIONAL. Include it ONLY IF the PR introduces significant data model/schema changes.
      "risks": [
        {
          "category": "ux|security|performance|maintenance|scalability|reliability|testability",
          "description": "Detailed description of the risk",
          "severity": "low|medium|high",
          "mitigation": "Actionable steps to address the risk. Provide a specific starting point or separate short-term/long-term actions (e.g., 'Short-term: Add specific monitoring for X. Long-term: Refactor Y to use Z pattern', or 'Implement stricter input validation on endpoint A'). Avoid generic advice and keep concise."
        }
      ]
    }
  ],
  "no_architecture_impact_reason": "string (max 5 words) - **ONLY if 'architectural_decisions' is empty.** Provide a concise, specific reason for no architectural impact (e.g., 'Minor dependency version update').",
  "no_decision_confidence_score": "float (0.0 to 1.0) - Your confidence that there are *no* significant architectural decisions in this PR. Provide this *only* if 'architectural_decisions' is empty. 1.0 = High confidence no decisions exist, 0.0 = Low confidence (suggests uncertainty)."
}

**Important Considerations:**

*   **Data Model Changes:** If the PR involves creating new database tables, collections, enums, or significantly altering existing ones (e.g., adding multiple indexed fields, changing relationships, adding/removing enum values), detail these in the \`data_model_changes\` array for the relevant architectural decision. Each string in the array should concisely describe one change. 
    *   For entities/tables: Use format \`"entity_name (field1:type, field2:type, ...) - created | modified | deleted"\`. Example: \`"Users (email:string, new_preference_field:jsonb, age:int) - modified"\` or \`"AuditLogs (id:uuid, action:text, user_id:uuid, timestamp:timestamptz) - created"\`.
    *   For enums: Use format \`"enum_name (enum: VALUE1, VALUE2, VALUE3) - created | modified | deleted"\`. Example: \`"OrderStatus (enum: PENDING, PROCESSING, SHIPPED, DELIVERED) - created"\` or \`"UserRole (enum: ADMIN, EDITOR, VIEWER) - modified"\`.
    *   Focus on architecturally significant schema changes. Ensure all significant data model modifications in the PR are listed to provide a complete picture.
*   **Confidence Score:** Assign the \`confidence_score\` based on how well the change aligns with the definition of a *significant* architectural decision (impact on structure, patterns, quality attributes, constraints, etc.). Routine changes or minor fixes should have low confidence, while major structural changes should have high confidence. Use the \`no_decision_confidence_score\` to express certainty when *no* decisions are found.
*   **Significance Threshold:** Be highly selective. Only include decisions that fundamentally alter structure, introduce/change major patterns, significantly impact quality attributes, or establish new system-wide constraints/conventions. Exclude routine updates, minor refactors confined to a single component, dependency bumps, or simple feature additions without broader architectural implications.
*   **Standard Practice Flag & Reason:** When marking a decision as \`follows_standard_practice\`, be highly confident that it represents a well-established industry norm (e.g., using JWT for authentication, implementing the Repository pattern for data access, using immutable data patterns). This should be based on clear evidence in the code or context that the approach follows widely-accepted best practices. **If you set \`follows_standard_practice\` to true, you MUST provide a concise pattern name and key file in the \`follows_standard_practice_reason\` field.**
*   **Rationale Quality:** Focus on the observable 'why' behind *this* decision based *only* on the available context (code, description, comments). State the connection between the change and the apparent goals or requirements directly.
*   **JSON Strictness:** Adhere precisely to the requested JSON format.
*   **Pattern Extensions:** If a change *extends* an existing architectural pattern to a new area, it *may* be significant if it broadens the pattern's application domain considerably or impacts critical quality attributes. Evaluate its significance based on the impact, not just the novelty. Clearly indicate if it's an extension using the 'is_extension' flag. Routine applications of established patterns are generally not significant unless they meet the broader impact criteria.
*   **Risk Identification:** Always explicitly identify risks in the structured "risks" array. Pay special attention to:
    *   **Vendor Lock-in:** When a decision introduces or deepens dependency on specific vendor services, explicitly add this as a "maintenance" category risk with appropriate severity.
    *   **Technology Lock-in:** Similar to vendor lock-in, but for specific technologies or frameworks that may limit future flexibility.
    *   **UX Risks:** When architectural decisions might negatively impact user experience (e.g., disabled but visible UI elements).
    *   Each risk must include a category, description, severity, and potential mitigation strategy. Mitigation should be specific and actionable (e.g., detail *what* to monitor, *which* component to refactor, or *what kind* of validation to add), not generic statements.
*   **Domain Concepts Hierarchy:** Structure domain concepts as a hierarchical array (max 4 levels) progressing from broad architectural categories to specific implementation details. Examples: ['UI Patterns', 'Navigation', 'Breadcrumb Management'] or ['Data Patterns', 'State Management', 'SWR Integration']. This enables conceptual drill-down from high-level architecture to specific patterns.
*   **Dev Prompt Format:** Keep dev prompts action-oriented and minimal. Focus on what developers should do when working in related areas, not explanations of what was implemented. Use format: "Context: specific action" (e.g., 'Creation flows: auto-navigate to newly created resource').
If no significant architectural decisions are identified in the provided diff based on the criteria above, return an empty \`architectural_decisions\` array and provide a clear, specific justification in the \`no_architecture_impact_reason\` field (max 5 words).`;
}

/**
 * Format code changes for the prompt
 * @param {Array} codeChanges - Code changes from PR files (expected to be objects with filename, additions, deletions, patch properties)
 * @returns {string} - Formatted code changes
 */
function formatCodeChanges(codeChanges: CodeChange[]): string {
  if (!codeChanges || codeChanges.length === 0) {
    // Provide a clearer message if the input is empty or invalid
    return 'No code changes provided or format is invalid.';
  }

  // Limit the number of files and patch size to avoid excessive prompt length
  const MAX_FILES_TO_SHOW = 5;
  const MAX_PATCH_LINES = 50; // Limit lines per patch

  const relevantChanges = codeChanges.slice(0, MAX_FILES_TO_SHOW);
  let result = 'Key code changes:\\n\\n';

  for (const file of relevantChanges) {
    // Defensive check for filename
    result += `File: ${file.filename || 'Unknown File'}\\n`;

    if (file.patch) {
      // Truncate patch if it's too long
      const patchLines = file.patch.split('\\n');
      const truncatedPatch = patchLines.slice(0, MAX_PATCH_LINES).join('\\n');
      result += 'Diff (potentially truncated):\\n```diff\\n';
      result += truncatedPatch;
      if (patchLines.length > MAX_PATCH_LINES) {
        result += '\\n... (patch truncated)';
      }
      result += '\\n```\\n';
    } else {
      result += '(No diff available for this file)\\n'; // Indicate if patch is missing
    }
    result += '\\n';
  }

  if (codeChanges.length > relevantChanges.length) {
    result += `...and ${codeChanges.length - relevantChanges.length} more file(s) with changes.\\n`;
  }

  return result;
}

// Helper function to format deployment constitution with only populated fields
function formatDeploymentConstitution(
  deploymentConstitution: any,
  includeGuidance = false
): string {
  if (!deploymentConstitution) {
    return '';
  }

  const {
    provider,
    compute,
    data_stores,
    containerization,
    deploymentPipeline,
    hosting_services,
    deployment_tools,
    monitoring_tools,
    serverless_functions,
    storage_services,
    compute_services,
  } = deploymentConstitution;

  const sections = [];
  const originalFieldCount = Object.keys(deploymentConstitution).length;

  // Only add fields that have meaningful values
  if (provider) {
    sections.push(`- **Hosting Provider:** ${provider}`);
  }

  if (compute?.length > 0) {
    sections.push(
      `- **Compute Environment:** ${compute.map((c: any) => c.type).join(', ')}`
    );
  }

  if (data_stores?.length > 0) {
    sections.push(
      `- **Existing Data Stores:** ${data_stores.map((d: any) => `${d.name} (${d.type})`).join(', ')}`
    );
  }

  if (containerization?.type) {
    const baseImageText = containerization.base_image
      ? ` (Base: ${containerization.base_image})`
      : '';
    sections.push(
      `- **Containerization:** ${containerization.type}${baseImageText}`
    );
  }

  if (deploymentPipeline?.deployed_to?.length > 0) {
    sections.push(
      `- **Deployment Pipeline:** ${deploymentPipeline.deployed_to.join(', ')}`
    );
  }

  if (deploymentPipeline?.has_tests !== undefined) {
    sections.push(
      `- **Testing Integration:** Automated tests are ${deploymentPipeline.has_tests ? 'configured' : 'not configured'}`
    );
  }

  if (serverless_functions?.length > 0) {
    const functions = serverless_functions
      .map((f: any) => `${f.name} (${f.provider})`)
      .join(', ');
    sections.push(`- **Serverless Functions:** ${functions}`);
  }

  if (storage_services?.length > 0) {
    const storage = storage_services
      .map((s: any) => `${s.name} (${s.type})`)
      .join(', ');
    sections.push(`- **Storage Services:** ${storage}`);
  }

  if (hosting_services?.length > 0) {
    sections.push(`- **Hosting Services:** ${hosting_services.join(', ')}`);
  }

  if (monitoring_tools?.length > 0) {
    sections.push(`- **Monitoring Tools:** ${monitoring_tools.join(', ')}`);
  }

  if (deployment_tools?.length > 0) {
    sections.push(`- **Deployment Tools:** ${deployment_tools.join(', ')}`);
  }

  if (compute_services?.length > 0) {
    const compute = compute_services
      .map((c: any) => `${c.name} (${c.type})`)
      .join(', ');
    sections.push(`- **Compute Services:** ${compute}`);
  }

  // Return empty string if no meaningful data
  if (sections.length === 0) {
    console.log(
      '[Deployment Constitution] No populated fields found, skipping deployment context'
    );
    return '';
  }

  // Log filtering information
  console.log(
    `[Deployment Constitution] Filtered deployment context: ${sections.length} populated fields out of ${originalFieldCount} total fields`
  );

  let result = sections.join('\n');

  // Add guidance section if requested
  if (includeGuidance) {
    result += '\n\n**🎯 DEPLOYMENT-AWARE DECISION CRITERIA:**\n';
    result +=
      '- **Prefer Existing Stack:** Options that leverage existing infrastructure should be strongly favored\n';
    result +=
      '- **Operational Complexity:** Consider deployment, monitoring, and maintenance overhead of new vs. existing technologies\n';
    result +=
      '- **Data Store Alignment:** When data storage is involved, prefer extending existing data stores over introducing new ones\n';
    result +=
      '- **Pipeline Compatibility:** Ensure new components integrate smoothly with existing deployment and testing pipelines\n';
    result +=
      '- **Monitoring Integration:** Consider how new components will be monitored and observed in the current environment\n';
  }

  return result;
}

/**
 * Generates a prompt to analyze the relationship between a new architectural decision
 * and potentially related existing decisions retrieved via RAG.
 *
 * @param {object} newDecision - The new architectural decision object (with metadata and Pinecone ID). Expected: { id, title, description, rationale, implications, ... }
 * @param {Array<object>} existingDecisions - An array of existing decision objects retrieved from RAG (including metadata and Pinecone ID). Expected: [{ id, metadata: { title, description, rationale, ... } }, ...]
 * @returns {string} - The prompt for the LLM.
 */
export function generateRelationshipAnalysisPrompt(
  newDecision: Decision,
  existingDecisions: any[]
): string | null {
  if (!newDecision || !existingDecisions || existingDecisions.length === 0) {
    console.warn(
      '[Relationship Prompt] Missing new decision or existing decisions for analysis.'
    );
    return null; // Or a default message indicating insufficient input
  }

  const newDecisionDetails = `
**New Architectural Decision (Under Analysis):**
- **ID:** ${newDecision.id || newDecision.pinecone_id || 'N/A'}
- **Title:** ${newDecision.title}
- **Description:** ${newDecision.description ? newDecision.description.substring(0, 500) + (newDecision.description.length > 500 ? '...' : '') : 'N/A'}
- **Related Files:** ${newDecision.related_files ? newDecision.related_files.join(', ') : 'N/A'}
- **Domain Concepts:** ${newDecision.domain_concepts ? newDecision.domain_concepts.join(', ') : 'N/A'}
  `;

  const existingDecisionsContext = existingDecisions
    .map(
      (d, index) => `
ID: ${d.id || d.metadata?.pinecone_id || 'N/A'}
Title: ${d.metadata?.title || 'N/A'}
Related Files: ${Array.isArray(d.metadata?.related_files) ? d.metadata.related_files.join(', ') : d.metadata?.related_files || 'N/A'}
Domain Concepts: ${Array.isArray(d.metadata?.domain_concepts) ? d.metadata.domain_concepts.join(', ') : d.metadata?.domain_concepts || 'N/A'}
PR Number: ${d.metadata?.pr_number || 'N/A'}
PR Merged At: ${d.metadata?.pr_merged_at ? new Date(d.metadata.pr_merged_at).toISOString().split('T')[0] : 'N/A'} 
Implications (Summary): ${d.metadata?.implications ? d.metadata.implications.substring(0, 300) + (d.metadata.implications.length > 300 ? '...' : '') : 'N/A'}
  `
    )
    .join('\\n');

  return `You are an expert software architect specializing in identifying how new architectural decisions relate to existing ones, with a strong focus on detecting when a new decision **supersedes** (i.e., replaces or makes obsolete) an older one.

**Task:**

Analyze the provided "New Architectural Decision" and determine if it **SUPERSEDES** any of the "Potential Existing Decisions" listed.

${newDecisionDetails}

**Potential Existing Decisions (from knowledge base):**
${existingDecisionsContext}

**Analysis Instructions:**

1.  **Primary Goal: Identify Supersession.** For each "Potential Existing Decision", carefully evaluate if the "New Architectural Decision" renders it obsolete, invalidates its core principles, or provides a clearly superior and replacing approach to the same problem or architectural aspect.
2.  **High Confidence for Supersedes:** Only flag a relationship as 'supersedes' if you are highly confident (e.g., > 0.85 confidence) that the new decision is intended to, and effectively does, replace the old one. Consider factors like:
    *   Does the new decision address the exact same problem or component in a fundamentally different or updated way?
    *   Does the rationale of the new decision implicitly or explicitly state or imply that an older approach is no longer valid or preferred?
    *   Do the implications of the new decision make the old decision's implications irrelevant or incorrect?
    *   Is there significant overlap in related files or domain concepts, where the new decision introduces a clear evolution or replacement?
3.  **Justification for Supersedes:** If you determine a 'supersedes' relationship, provide a concise justification (1-2 sentences) explaining *why* the new decision supersedes the existing one, referencing specific aspects of both decisions.
4.  **No Other Relationship Types:** For this task, **DO NOT** identify other relationship types like 'amends', 'conflicts_with', 'related_to', etc. If the new decision does not clearly supersede an existing one, classify it as 'independent' relative to that specific existing decision but do not include in output.
5.  **Independence:** If the "New Architectural Decision" is distinct and does not supersede a "Potential Existing Decision", mark its relationship_type as 'independent' but do not include in output.

**Output Format:**

Provide your analysis as a JSON object with the following structure:
{
  "relationship_analysis": {
    "new_decision_id": "${newDecision.id || newDecision.pinecone_id || 'NEW_DECISION_ID_PLACEHOLDER'}",
    "relationships": [
      {
        "existing_decision_id": "ID_OF_EXISTING_DECISION_1",
        "relationship_type": "supersedes", // STRICTLY 'supersedes' (otherwise do not include in output)
        "confidence_score": "float (0.0 to 1.0) - Your confidence in this specific relationship assessment. For 'supersedes', this score should be high (e.g., >0.85). For 'independent', it can reflect certainty of independence.",
        "justification": "5 words at the max" (REQUIRED if relationship_type is 'supersedes'"
      }
      // ... more relationship objects for each existing decision analyzed
    ]
  }
}

**Important Considerations:**

*   **Accuracy of 'supersedes':** It is critical that 'supersedes' relationships are identified accurately. Erroneously marking a decision as superseded can hide valuable current information
*   **Focus:** Your sole focus is to identify clear supersession. Do not infer other relationships.
*   **Concise Justifications:** Keep justifications for 'supersedes' brief and to the point, highlighting the core reason for replacement.
*   **JSON Strictness:** Adhere precisely to the requested JSON format. Ensure 'relationship_type' is ONLY "supersedes" or "independent".
`;
}
