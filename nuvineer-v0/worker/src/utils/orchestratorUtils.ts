// orchestratorUtils.ts - TypeScript implementation of orchestrator functions

import crypto from 'crypto';

// Import required utilities
import { callClaudeLlmFor<PERSON>son } from './llmUtils';
import { supabaseAdmin } from './supabaseClient';
import {
  storeDecisionRecord,
  getAllDecisionMetadataForRepo as getVectorDecisionMetadata,
  findDecisionsByDomainConcepts,
  findDecisionsByFileOverlap,
  findDecisionsByImpactOverlap,
  markDecisionAsSuperseded,
} from './vectorOperations';
import {
  PRContext,
  CodeChange,
  Comment,
  ProcessMergedPRResult,
  GetAllDecisionMetadataOptions,
  DecisionMetadata,
  Decision,
  RelationshipAnalysisResult,
} from '../types';
import { logger } from './logger';
import { generatePrompt, generateRelationshipAnalysisPrompt } from './prompt';

/**
 * Extracts installation ID and repository slug from namespace
 * This enables gradual migration from namespace-based calls to installationId/repositorySlug calls
 */
export function extractParamsFromNamespace(namespace: string): {
  installationId: number;
  repositorySlug: string;
} {
  if (!namespace) {
    throw new Error('Namespace is required');
  }

  const parts = namespace.split('-');
  if (parts.length < 3) {
    throw new Error(`Cannot parse namespace format: ${namespace}`);
  }

  const installationId = parseInt(parts[0]);
  if (isNaN(installationId)) {
    throw new Error(`Invalid installation ID in namespace: ${parts[0]}`);
  }

  // Extract repository slug from namespace format: {installationId}-{owner}--{repo}
  const ownerRepoPart = parts.slice(1).join('-');
  const ownerRepoMatch = ownerRepoPart.match(/^(.+)--(.+)$/);

  if (!ownerRepoMatch) {
    throw new Error(
      `Invalid namespace format for repo extraction: ${namespace}`
    );
  }

  const repositorySlug = `${ownerRepoMatch[1]}/${ownerRepoMatch[2]}`;

  return { installationId, repositorySlug };
}

/**
 * Validates a GitHub webhook signature.
 */
export function validateGitHubSignature(
  payload: string,
  signature: string,
  secret: string
): boolean {
  try {
    const expectedSignature = `sha256=${crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex')}`;

    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );
  } catch (error) {
    logger.error(
      '[orchestratorUtils] Error validating GitHub signature:',
      error
    );
    return false;
  }
}

/**
 * Logs the outcome of processing a PR.
 */
export async function logProcessedPR(
  prContext: PRContext,
  status: string,
  decisionsCount: number,
  errorMessage: string = '',
  decisions: Decision[] = []
): Promise<void> {
  try {
    logger.info(
      `[ProcessedPR] ${prContext.html_url} - Status: ${status}, Decisions: ${decisionsCount}`
    );

    if (errorMessage) {
      if (status === 'failed' || status === 'completed_with_errors') {
        logger.error(`[ProcessedPR] Error: ${errorMessage}`);
      } else {
        logger.info(`[ProcessedPR] ${errorMessage}`);
      }
    }

    // Log decision details if provided
    if (decisions.length > 0) {
      logger.info(
        `[ProcessedPR] Decision details:`,
        decisions.map(d => ({ title: d.title, id: d.id }))
      );
    }
  } catch (error) {
    logger.error('[orchestratorUtils] Error in logProcessedPR:', error);
  }
}

/**
 * Processes a merged Pull Request to extract and analyze architectural decisions.
 * This is a TypeScript implementation with core orchestrator logic.
 */
export async function processMergedPR(
  prContext: PRContext,
  codeChanges: CodeChange[],
  comments: Comment[],
  namespace: string,
  installationRepositorySlug: string,
  designDocContent?: string,
  skipRelationshipAnalysis: boolean = false
): Promise<ProcessMergedPRResult> {
  const startTime = Date.now();
  const logPrefix = `[ProcessMergedPR - ${
    prContext?.number || 'unknown'
  } - ${installationRepositorySlug}]`;

  try {
    // Extract installationId from namespace for modern vector operations
    let installationId: number;
    try {
      const extracted = extractParamsFromNamespace(namespace);
      installationId = extracted.installationId;
      logger.info(
        `${logPrefix} Extracted installationId: ${installationId} from namespace: ${namespace}`
      );
    } catch (error) {
      logger.error(
        `${logPrefix} Failed to extract installationId from namespace: ${namespace}`,
        error
      );
      throw new Error(`Invalid namespace format: ${namespace}`);
    }

    logger.info(
      `[Orchestrator] Starting analysis for PR #${prContext.number} in ${installationRepositorySlug} (isPseudo: ${prContext.is_pseudo_pr}).`
    );
    logger.info(`${logPrefix} Starting deep analysis for ${prContext.title}`);
    logger.info(
      `${logPrefix} Repository: ${installationRepositorySlug}, InstallationId: ${installationId}`
    );
    logger.info(
      `${logPrefix} Code changes: ${codeChanges.length} files, Comments: ${comments.length}`
    );

    // Fetch Deployment Constitution
    let deploymentConstitution = null;
    try {
      if (supabaseAdmin) {
        const { data: constitutionData, error: constitutionError } =
          await supabaseAdmin
            .from('deployment_constitutions')
            .select('constitution_data')
            .eq('repository_slug', installationRepositorySlug)
            .single();

        if (constitutionError) {
          if (constitutionError.code !== 'PGRST116') {
            // PGRST116 = "No rows found"
            logger.warn(
              `${logPrefix} Error fetching deployment constitution:`,
              constitutionError.message
            );
          }
        } else if (constitutionData) {
          deploymentConstitution = constitutionData.constitution_data;
          logger.info(
            `${logPrefix} Successfully loaded Deployment Constitution.`
          );
        }
      }
    } catch (err) {
      logger.error(
        `${logPrefix} Unexpected error fetching deployment constitution:`,
        err
      );
    }

    logger.info(
      `${logPrefix} ==================== DEEP ANALYSIS ====================`
    );
    logger.info(
      `${logPrefix} Beginning comprehensive architectural decision analysis...`
    );

    let decisions = [];
    let decisionsCount = 0;
    let status = 'processing';
    let errorMessage = '';

    // Generate prompt and call LLM
    logger.debug(
      `[Orchestrator] Standard run. Generating ADR extraction prompt.`
    );
    const prompt = generatePrompt(
      prContext,
      codeChanges,
      comments,
      deploymentConstitution
    );
    const result = await callClaudeLlmForJson(
      prompt,
      'claude-sonnet-4-20250514'
    );

    // Validate the primary response structure
    if (!result || !Array.isArray(result.architectural_decisions)) {
      logger.error(
        `[Orchestrator] Invalid LLM response structure: ${JSON.stringify(
          result
        )}`
      );
      throw new Error(
        `Invalid LLM response structure. Expected 'architectural_decisions' array. Received: ${JSON.stringify(
          result
        )}`
      );
    }

    decisions = result.architectural_decisions || [];
    decisionsCount = decisions.length;
    const noDecisionReason = result.no_architecture_impact_reason || null;

    if (decisionsCount === 0) {
      logger.debug(
        `[Orchestrator] No significant architectural decisions identified. Reason: ${
          noDecisionReason || 'None provided'
        }`
      );
      status = 'completed_no_decisions';
      errorMessage = noDecisionReason;
      await logProcessedPR(prContext, status, decisionsCount, errorMessage);
      return {
        status,
        decisionsCount,
        reason: errorMessage,
        duration: Date.now() - startTime,
      };
    }

    logger.info(
      `[Orchestrator] Extracted ${decisionsCount} potential architectural decision(s).`
    );

    // Process each decision: enrich and prepare for storage
    const processedDecisions = [];
    const processingErrors = [];

    for (let i = 0; i < decisions.length; i++) {
      let decision = decisions[i];
      const decisionNumber = i + 1;
      logger.debug(
        `[Orchestrator] Processing decision ${decisionNumber}/${decisionsCount}: "${decision.title}"`
      );

      try {
        // Add metadata before storage/analysis
        decision.pr_number = prContext.number;
        decision.pr_url = prContext.html_url;
        decision.pr_merged_at = prContext.merged_at
          ? Date.parse(prContext.merged_at)
          : Date.now();
        decision.repository_slug = installationRepositorySlug;
        decision.extracted_at = new Date().toISOString();
        decision.pr_author =
          typeof prContext.user === 'object'
            ? prContext.user.login
            : prContext.user || 'unknown';
        decision.pr_title = prContext.title;
        decision.is_superseded = false;

        // --- Relationship Analysis (Forward-Looking) ---
        logger.debug(
          `[Orchestrator] Performing forward-looking relationship analysis for "${decision.title}".`
        );
        try {
          const namespace = `${installationId}_${installationRepositorySlug}`;
          const relationshipAnalysis = await analyzeRelationships(
            decision,
            namespace,
            { direction: 'forward' }
          );
          const supersedingRelationship =
            relationshipAnalysis.relationships?.find(
              rel => rel.relationship_type === 'supersedes'
            );

          if (supersedingRelationship) {
            decision.is_superseded = true;
            decision.superseded_by_decision_id =
              supersedingRelationship.existing_decision_id;
            logger.debug(
              `[Orchestrator] Decision "${decision.title}" marked as superseded by ${supersedingRelationship.existing_decision_id}.`
            );
          } else {
            decision.is_superseded = false;
          }
          decision.relationships_analysis =
            JSON.stringify(relationshipAnalysis); // Store for debugging/future use
        } catch (relationshipError) {
          logger.warn(
            `[Orchestrator] Error during relationship analysis for "${decision.title}":`,
            relationshipError
          );
          // Continue without relationship analysis if it fails
          decision.relationships_analysis = JSON.stringify({
            relationships: [],
            potential_matches_for_analysis: [],
            error:
              relationshipError instanceof Error
                ? relationshipError.message
                : 'Unknown error',
          });
        }

        // Store the decision in the vector database
        try {
          const vectorId = await storeDecisionRecord(
            decision,
            installationId,
            installationRepositorySlug
          );
          decision.vector_id = vectorId;

          logger.debug(
            `[Orchestrator] Decision "${decision.title}" stored successfully with vector ID: ${vectorId}`
          );

          // Handle superseding relationships if any were identified
          if (decision.relationships_analysis) {
            try {
              const relationshipData = JSON.parse(
                decision.relationships_analysis
              );
              if (relationshipData.relationships) {
                const currentDecisionSupersedes =
                  relationshipData.relationships.filter(
                    (rel: any) => rel.relationship_type === 'supersedes'
                  );
                if (currentDecisionSupersedes.length > 0) {
                  logger.debug(
                    `[Orchestrator] Decision "${decision.title}" (${vectorId}) supersedes ${currentDecisionSupersedes.length} other decision(s). Marking them in vector database.`
                  );
                  for (const rel of currentDecisionSupersedes) {
                    await markDecisionAsSuperseded(
                      rel.existing_decision_id,
                      vectorId,
                      installationId,
                      installationRepositorySlug
                    );
                  }
                }
              }
            } catch (relationshipProcessingError) {
              logger.warn(
                `[Orchestrator] Error processing superseding relationships for "${decision.title}":`,
                relationshipProcessingError
              );
            }
          }
        } catch (storageError) {
          logger.error(
            `[Orchestrator] Error storing decision "${decision.title}" in vector database:`,
            storageError
          );
          // Continue processing even if storage fails - the decision is still valid
          logger.debug(
            `[Orchestrator] Decision "${decision.title}" processed successfully (storage failed but continuing)`
          );
        }

        processedDecisions.push(decision);
      } catch (decisionError) {
        logger.error(
          `[Orchestrator] Error processing decision "${decision.title}":`,
          decisionError
        );
        processingErrors.push({
          title: decision.title,
          error:
            decisionError instanceof Error
              ? decisionError.message
              : String(decisionError),
        });
      }
    }

    // Determine final status based on processing errors
    if (processingErrors.length > 0) {
      if (processingErrors.length === decisionsCount) {
        status = 'failed';
        errorMessage = `All ${decisionsCount} decisions failed processing. See logs for details. First error: ${processingErrors[0].error}`;
      } else {
        status = 'completed_with_errors';
        errorMessage = `${
          processingErrors.length
        } out of ${decisionsCount} decisions failed processing. See logs. Errors: ${JSON.stringify(
          processingErrors
        )}`;
      }
    } else {
      status = 'completed_successfully';
      errorMessage = `Successfully processed ${processedDecisions.length} decision(s).`;
    }

    logger.info(
      `[Orchestrator] Finished processing PR ${prContext.html_url}. Status: ${status}`
    );
    await logProcessedPR(
      prContext,
      status,
      processedDecisions.length,
      errorMessage,
      processedDecisions
    );

    return {
      status,
      decisions: processedDecisions,
      duration: Date.now() - startTime,
      errors: processingErrors,
    };
  } catch (error) {
    logger.error(
      `[Orchestrator] Unhandled error during PR processing for ${prContext.html_url}:`,
      error
    );
    const status = 'failed';
    const errorMessage =
      error instanceof Error
        ? error.message
        : 'An unknown error occurred during processing.';
    await logProcessedPR(prContext, status, 0, errorMessage);
    return {
      status,
      error: errorMessage,
      decisions: [],
      duration: Date.now() - startTime,
    };
  }
}

/**
 * Retrieves all decision metadata for a given repository.
 * Uses the vector operations to get decisions from the vector database.
 */
export async function getAllDecisionMetadataForRepo(
  installationId: number,
  repositorySlug: string,
  options: GetAllDecisionMetadataOptions = {}
): Promise<DecisionMetadata[]> {
  const logPrefix = `[Orchestrator getAllDecisionMetadata - Repo: ${repositorySlug}]`;
  logger.debug(`${logPrefix} Fetching all decisions`);

  if (typeof installationId !== 'number' || !repositorySlug) {
    logger.error(`${logPrefix} Missing required parameters.`);
    return [];
  }

  try {
    // Use the vector operations to get decision metadata
    const decisions = await getVectorDecisionMetadata(
      installationId,
      repositorySlug,
      options
    );
    logger.debug(
      `${logPrefix} Retrieved ${decisions.length} decisions from vector database`
    );
    return decisions;
  } catch (error) {
    logger.error(`${logPrefix} Error fetching decision metadata:`, error);
    return [];
  }
}

// Add analyzeRelationships function stub if it doesn't exist, or ensure it's imported/defined
export async function analyzeRelationships(
  decision: Decision,
  targetNamespace: string,
  options: { direction?: string } = {}
): Promise<RelationshipAnalysisResult> {
  // No longer optional // MODIFIED: Added export
  const { direction = 'backward' } = options;
  if (!targetNamespace) {
    console.error(
      `[analyzeRelationships - Dec: "${decision.title}"] CRITICAL: targetNamespace not provided. Aborting analysis.`
    );
    return {
      relationships: [],
      potential_matches_for_analysis: [],
      error: 'targetNamespace is required for analyzeRelationships.',
    };
  }
  const currentActiveNamespace = targetNamespace;
  console.log(
    `[Orchestrator] Analyzing relationships for decision "${decision.title}" in namespace ${currentActiveNamespace}.`
  );

  try {
    // CRITICAL: Get the current decision's merge time to enforce chronological constraint
    const currentDecisionMergeTime = decision.pr_merged_at;
    if (!currentDecisionMergeTime) {
      console.error(
        `[Relationship Analysis] CRITICAL: Decision "${decision.title}" has no pr_merged_at timestamp. Cannot enforce chronological constraint.`
      );
      return {
        relationships: [],
        potential_matches_for_analysis: [],
        error: 'Missing pr_merged_at timestamp for chronological constraint.',
      };
    }

    // Convert to timestamp if it's a string
    const currentTimestamp =
      typeof currentDecisionMergeTime === 'string'
        ? new Date(currentDecisionMergeTime).getTime()
        : currentDecisionMergeTime;

    console.log(
      `[Relationship Analysis] Current decision "${decision.title}" merge timestamp: ${currentTimestamp} (${new Date(currentTimestamp).toISOString()})`
    );

    // 1. Find potentially related decisions (by concepts, file overlap, etc.)
    // Use abstracted functions with proper parameters
    const { installationId, repositorySlug } = extractParamsFromNamespace(
      currentActiveNamespace
    );
    const relatedByConcept = await findDecisionsByDomainConcepts(
      decision,
      installationId,
      repositorySlug
    );
    const relatedByFile = await findDecisionsByFileOverlap(
      decision,
      installationId,
      repositorySlug
    );
    const relatedByImpact = await findDecisionsByImpactOverlap(
      decision,
      installationId,
      repositorySlug
    );

    // Combine and deduplicate potential matches based on ID, prioritizing by relevance
    const potentialMatchesMap = new Map();
    const decisionIdToExclude = decision.id || decision.pinecone_id;

    const addMatchesToMap = (matches: Decision[]) => {
      matches.forEach(match => {
        if (
          match.id !== decisionIdToExclude &&
          !potentialMatchesMap.has(match.id)
        ) {
          potentialMatchesMap.set(match.id, match);
        }
      });
    };

    // Prioritize by impact similarity (highest signal), then file overlap, then concepts
    addMatchesToMap(relatedByImpact);
    addMatchesToMap(relatedByFile);
    addMatchesToMap(relatedByConcept);

    const allPotentialMatches = Array.from(potentialMatchesMap.values());

    // CRITICAL: Filter to only include decisions that are chronologically relevant based on direction
    const chronologicallyValidCandidates = allPotentialMatches.filter(match => {
      const matchMergeTime = match.metadata?.pr_merged_at;
      if (!matchMergeTime) {
        console.warn(
          `[Relationship Analysis] Skipping decision ${match.id} - no pr_merged_at timestamp.`
        );
        return false;
      }

      const matchTimestamp =
        typeof matchMergeTime === 'string'
          ? new Date(matchMergeTime).getTime()
          : matchMergeTime;

      const isChronologicallyValid =
        direction === 'backward'
          ? matchTimestamp < currentTimestamp
          : matchTimestamp > currentTimestamp;

      if (!isChronologicallyValid) {
        console.debug(
          `[Relationship Analysis] Excluding decision ${match.id} - not chronologically valid for ${direction} analysis.`
        );
      }
      return isChronologicallyValid;
    });

    const filteredCount =
      allPotentialMatches.length - chronologicallyValidCandidates.length;
    if (filteredCount > 0) {
      console.log(
        `[Relationship Analysis] Chronological constraint: Filtered out ${filteredCount} decisions for direction '${direction}'.`
      );
    }

    // Limit the number of candidates to avoid exceeding context window
    const MAX_RELATIONSHIP_CANDIDATES = 15;
    let finalCandidates = chronologicallyValidCandidates;
    if (chronologicallyValidCandidates.length > MAX_RELATIONSHIP_CANDIDATES) {
      console.log(
        `[Relationship Analysis] Too many candidates (${chronologicallyValidCandidates.length}). Capping to the top ${MAX_RELATIONSHIP_CANDIDATES} most relevant ones.`
      );
      finalCandidates = chronologicallyValidCandidates.slice(
        0,
        MAX_RELATIONSHIP_CANDIDATES
      );
    }

    if (finalCandidates.length === 0) {
      console.log(
        `[Relationship Analysis] No chronologically valid decisions found for "${decision.title}" (ID: ${decisionIdToExclude}) for direction '${direction}'.`
      );
      return { relationships: [], potential_matches_for_analysis: [] };
    }

    console.log(
      `[Relationship Analysis] Found ${finalCandidates.length} potentially related decisions for "${decision.title}" (ID: ${decisionIdToExclude}) for direction '${direction}'.`
    );

    // 2. Generate prompt for relationship analysis
    const relationshipPrompt = generateRelationshipAnalysisPrompt(
      decision,
      finalCandidates
    );
    if (!relationshipPrompt) {
      console.warn(
        `[Relationship Analysis] Skipping LLM call as no potential relationships were found or prompt generation failed for "${decision.title}".`
      );
      return {
        relationships: [],
        potential_matches_for_analysis: finalCandidates,
      };
    }

    // 3. Call LLM
    console.log(
      `[Relationship Analysis] Calling LLM to analyze relationships for "${decision.title}" against ${finalCandidates.length} candidate decisions.`
    );
    const analysisResult = await callClaudeLlmForJson(
      relationshipPrompt,
      'claude-sonnet-4-20250514'
    ); // Expects JSON

    // 4. Validate and return result
    if (
      analysisResult &&
      analysisResult.relationship_analysis &&
      Array.isArray(analysisResult.relationship_analysis.relationships)
    ) {
      console.log(
        `[Relationship Analysis] LLM analysis successful for "${decision.title}".`
      );

      // --- ADDED FILTERING ---
      const allIdentifiedRelationships =
        analysisResult.relationship_analysis.relationships;
      const supersedesRelationships = allIdentifiedRelationships.filter(
        (rel: any) => rel.relationship_type === 'supersedes'
      );

      if (supersedesRelationships.length < allIdentifiedRelationships.length) {
        console.log(
          `[Relationship Analysis] Filtered LLM results for "${decision.title}": Kept ${supersedesRelationships.length} 'supersedes' relationships out of ${allIdentifiedRelationships.length} total identified.`
        );
      }
      // --- END ADDED FILTERING ---

      // Pass along potentialMatches so the caller can access metadata like dev_prompt
      // Return the filtered list of relationships
      return {
        relationships: supersedesRelationships,
        potential_matches_for_analysis: finalCandidates,
      };
    } else {
      console.error(
        `[Relationship Analysis] Invalid LLM response structure for "${decision.title}":`,
        analysisResult
      );
      // Still return potential matches even if LLM fails, so caller knows what was considered
      return {
        relationships: [],
        potential_matches_for_analysis: finalCandidates,
        error: 'Invalid LLM response structure',
      };
    }
  } catch (error) {
    console.error(
      `[Relationship Analysis] Error during relationship analysis for "${decision.title}":`,
      error
    );
    // Return empty on error to avoid blocking the whole process, but include error info
    return {
      relationships: [],
      potential_matches_for_analysis: [],
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
