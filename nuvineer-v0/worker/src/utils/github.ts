import { Octokit } from '@octokit/rest';
import { logger } from './logger';
import dotenv from 'dotenv';
import {
  PRDetails,
  PRFile,
  PRComment,
  RecentPRsOptions,
  RecentPRsResponse,
  RecentCommitsOptions,
  CommitDetail,
  PseudoPR,
} from '../types';

dotenv.config();

// Initialize GitHub client
const octokit = new Octokit({
  auth: process.env.GITHUB_TOKEN,
});

export function getHeaders(authToken?: string): Record<string, string> {
  const headers: Record<string, string> = {
    Accept: 'application/vnd.github.v3+json',
  };

  if (authToken) {
    headers['Authorization'] = `token ${authToken}`;
  } else if (process.env.GITHUB_TOKEN) {
    // Use GITHUB_TOKEN for public repositories
    headers['Authorization'] = `token ${process.env.GITHUB_TOKEN}`;
  }

  return headers;
}

/**
 * Get PR details
 * @param owner - Repository owner
 * @param repo - Repository name
 * @param prNumber - Pull request number
 * @param authToken - Optional auth token
 * @returns PR details
 */
export async function getPR(
  owner: string,
  repo: string,
  prNumber: number,
  authToken: string | null = null
): Promise<PRDetails> {
  const url = `https://api.github.com/repos/${owner}/${repo}/pulls/${prNumber}`;
  try {
    logger.debug(`Fetching PR #${prNumber} from ${owner}/${repo}`);
    const response = await fetch(url, {
      headers: getHeaders(authToken || undefined),
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error(
        `Error fetching PR #${prNumber}: ${response.status} ${response.statusText}. Response: ${errorText}`
      );
      throw new Error(
        `GitHub API error: ${response.status} ${response.statusText}`
      );
    }

    const pr = await response.json();
    logger.debug(`Successfully fetched PR #${prNumber}`);

    return {
      number: pr.number,
      title: pr.title,
      body: pr.body || '',
      state: pr.state,
      author: pr.user.login,
      created_at: pr.created_at,
      merged_at: pr.merged_at,
      merged: !!pr.merged_at,
      html_url: pr.html_url,
      head: {
        sha: pr.head.sha,
        ref: pr.head.ref,
      },
      base: {
        sha: pr.base.sha,
        ref: pr.base.ref,
      },
    };
  } catch (error) {
    logger.error(`Error fetching PR #${prNumber}:`, error);
    throw error;
  }
}

/**
 * Get PR files including diffs
 * @param owner - Repository owner
 * @param repo - Repository name
 * @param prNumber - Pull request number
 * @param authToken - Optional auth token
 * @returns PR files with diffs
 */
export async function getPRFiles(
  owner: string,
  repo: string,
  prNumber: number,
  authToken: string | null = null
): Promise<PRFile[]> {
  const url = `https://api.github.com/repos/${owner}/${repo}/pulls/${prNumber}/files`;
  try {
    logger.debug(`Fetching files for PR #${prNumber}`);
    const response = await fetch(url, {
      headers: getHeaders(authToken || undefined),
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error(
        `Error fetching files for PR #${prNumber}: ${response.status} ${response.statusText}. Response: ${errorText}`
      );
      throw new Error(
        `GitHub API error: ${response.status} ${response.statusText}`
      );
    }

    const files = await response.json();
    logger.debug(
      `Successfully fetched ${files.length} files for PR #${prNumber}`
    );

    // Process files to include diffs and key changes
    const prFiles: PRFile[] = [];

    for (const file of files) {
      const fileInfo: PRFile = {
        filename: file.filename,
        status: file.status,
        additions: file.additions,
        deletions: file.deletions,
        changes: file.changes,
        patch: file.patch || '', // Include the diff patch if available
      };

      // Extract key changes from the patch
      if (file.patch) {
        fileInfo.key_changes = extractKeyChanges(file.patch);
      }

      prFiles.push(fileInfo);
    }

    return prFiles;
  } catch (error) {
    logger.error(`Error fetching files for PR #${prNumber}:`, error);
    throw error;
  }
}

/**
 * Extract key changes from a diff patch
 * @param patch - Diff patch
 * @returns Key changes extracted from the patch
 */
function extractKeyChanges(patch: string): string[] {
  if (!patch) return [];

  const keyChanges: string[] = [];
  const lines = patch.split('\n');
  let inAddition = false;
  let currentAddition = '';

  // Look for meaningful changes in the diff
  for (const line of lines) {
    // Skip diff metadata lines
    if (
      line.startsWith('@@') ||
      line.startsWith('diff') ||
      line.startsWith('index')
    ) {
      continue;
    }

    if (line.startsWith('+') && !line.startsWith('++')) {
      // This is an addition
      inAddition = true;
      currentAddition += line.substring(1) + '\n';
    } else if (inAddition) {
      // End of an addition block
      inAddition = false;

      // Only include meaningful additions (non-empty, not just whitespace or comments)
      if (isSignificantChange(currentAddition)) {
        keyChanges.push(currentAddition.trim());
      }

      currentAddition = '';
    }
  }

  // Handle any remaining addition block
  if (inAddition && isSignificantChange(currentAddition)) {
    keyChanges.push(currentAddition.trim());
  }

  // Return up to 5 most significant changes
  return keyChanges
    .filter(change => change.length > 0)
    .sort((a, b) => b.length - a.length)
    .slice(0, 5);
}

/**
 * Check if a code change is significant
 * @param change - Code change
 * @returns True if the change is significant
 */
function isSignificantChange(change: string): boolean {
  if (!change || change.trim().length === 0) return false;

  // Skip comments-only changes
  const trimmed = change.trim();
  if (
    trimmed.startsWith('//') ||
    trimmed.startsWith('/*') ||
    trimmed.startsWith('*') ||
    trimmed.startsWith('#') ||
    trimmed.startsWith('<!--')
  ) {
    return false;
  }

  // Skip import-only changes if they're the only change
  if (trimmed.startsWith('import ') || trimmed.startsWith('require(')) {
    return false;
  }

  // Skip whitespace-only changes
  if (/^\s*$/.test(trimmed)) {
    return false;
  }

  return true;
}

/**
 * Get PR comments
 * @param owner - Repository owner
 * @param repo - Repository name
 * @param prNumber - Pull request number
 * @param authToken - Optional auth token
 * @returns PR comments
 */
export async function getPRComments(
  owner: string,
  repo: string,
  prNumber: number,
  authToken: string | null = null
): Promise<PRComment[]> {
  const issueCommentsUrl = `https://api.github.com/repos/${owner}/${repo}/issues/${prNumber}/comments`;
  const reviewCommentsUrl = `https://api.github.com/repos/${owner}/${repo}/pulls/${prNumber}/comments`;

  try {
    logger.debug(`Fetching comments for PR #${prNumber}`);

    // Fetch both types of comments in parallel
    const [issueResponse, reviewResponse] = await Promise.all([
      fetch(issueCommentsUrl, { headers: getHeaders(authToken || undefined) }),
      fetch(reviewCommentsUrl, { headers: getHeaders(authToken || undefined) }),
    ]);

    if (!issueResponse.ok) {
      const errorText = await issueResponse.text();
      logger.error(
        `Error fetching issue comments: ${issueResponse.status} ${issueResponse.statusText}. Response: ${errorText}`
      );
    }

    if (!reviewResponse.ok) {
      const errorText = await reviewResponse.text();
      logger.error(
        `Error fetching review comments: ${reviewResponse.status} ${reviewResponse.statusText}. Response: ${errorText}`
      );
    }

    // Get JSON from responses that were ok
    const issueComments = issueResponse.ok ? await issueResponse.json() : [];
    const reviewComments = reviewResponse.ok ? await reviewResponse.json() : [];

    // Combine both types of comments
    const allComments = [...issueComments, ...reviewComments];

    return allComments;
  } catch (error) {
    logger.error(`Error fetching comments for PR #${prNumber}:`, error);
    // Return empty array instead of throwing so we don't fail the whole analysis if comments fail
    return [];
  }
}

/**
 * Get PRs with optional date filtering and pagination.
 * @param owner - Repository owner
 * @param repo - Repository name
 * @param options - Filter options
 * @param authToken - Optional auth token
 * @returns Filtered PRs or object with PRs and pagination info
 */
export async function getRecentPRs(
  owner: string,
  repo: string,
  options: RecentPRsOptions = {},
  authToken: string | null = null
): Promise<any[] | RecentPRsResponse> {
  const {
    maxPRs = 100,
    count,
    branch = null,
    sinceDate = null,
    page = 1,
    per_page = 100,
    state = 'closed',
    sort = 'updated',
    direction = 'desc',
    returnPaginationInfo = false,
  } = options;

  // Handle backward compatibility: use count if provided, otherwise use maxPRs
  const effectiveMaxPRs = count !== undefined ? count : maxPRs;

  // Construct the base URL with pagination and sorting
  let url = `https://api.github.com/repos/${owner}/${repo}/pulls?state=${state}&sort=${sort}&direction=${direction}&page=${page}&per_page=${per_page}`;

  // Add branch parameter if provided
  if (branch) {
    url += `&base=${encodeURIComponent(branch)}`;
  }

  try {
    logger.debug(
      `Fetching PRs from ${owner}/${repo} - page ${page} with ${per_page} per page`
    );
    if (sinceDate) logger.debug(`Filtering PRs newer than: ${sinceDate}`);

    const response = await fetch(url, {
      headers: getHeaders(authToken || undefined),
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error(
        `Error fetching recent PRs: ${response.status} ${response.statusText}. Response: ${errorText}`
      );
      throw new Error(
        `GitHub API error: ${response.status} ${response.statusText}`
      );
    }

    let allPRs = await response.json();

    // Store original count for pagination info
    const originalPRCount = allPRs.length;

    // Filter to only include merged PRs
    const mergedPRs = allPRs.filter((pr: any) => pr.merged_at !== null);

    // Apply sinceDate filter if provided
    let filteredPRs = mergedPRs;
    if (sinceDate) {
      const sinceTimestamp = new Date(sinceDate).getTime();
      filteredPRs = mergedPRs.filter((pr: any) => {
        const prMergedTimestamp = new Date(pr.merged_at).getTime();
        return prMergedTimestamp >= sinceTimestamp;
      });
    }

    // Apply effectiveMaxPRs limit only if it's less than the per_page size
    // This allows pagination to work correctly when called multiple times
    const limitedPRs =
      effectiveMaxPRs < per_page
        ? filteredPRs.slice(0, effectiveMaxPRs)
        : filteredPRs;

    logger.debug(
      `Found ${originalPRCount} total PRs from GitHub, ${mergedPRs.length} merged PRs on page ${page}, ${filteredPRs.length} after date filtering, returning ${limitedPRs.length}`
    );

    // Return pagination info if requested
    if (returnPaginationInfo) {
      return {
        prs: limitedPRs,
        hasMore: originalPRCount === per_page, // More pages available if GitHub returned full page size
        totalFromGitHub: originalPRCount,
        mergedCount: mergedPRs.length,
        filteredCount: filteredPRs.length,
      };
    }

    return limitedPRs;
  } catch (error) {
    logger.error(`Error fetching filtered PRs for ${owner}/${repo}:`, error);
    throw error;
  }
}

/**
 * Fetches recent commits from the main/master branch of a repository.
 *
 * @param owner - Repository owner
 * @param repo - Repository name
 * @param options - Options for fetching commits
 * @param authToken - Auth token for GitHub API
 * @returns Array of commit objects
 */
export async function getRecentCommits(
  owner: string,
  repo: string,
  options: RecentCommitsOptions = {},
  authToken: string | null = null
): Promise<any[]> {
  const {
    maxCommits = 100,
    sinceDate = null,
    branch = null,
    startPage = 1,
  } = options;

  logger.debug(
    `Fetching up to ${maxCommits} recent commits for ${owner}/${repo} starting from page ${startPage}`
  );

  try {
    // Get the default branch name if no branch specified
    let targetBranch = branch;
    if (!targetBranch) {
      try {
        const repoUrl = `https://api.github.com/repos/${owner}/${repo}`;
        const repoResponse = await fetch(repoUrl, {
          headers: getHeaders(authToken || undefined),
        });

        if (repoResponse.ok) {
          const repoData = await repoResponse.json();
          targetBranch = repoData.default_branch;
          logger.debug(`Using repository default branch: ${targetBranch}`);
        } else {
          logger.warn(
            `Failed to fetch repository info to determine default branch. Falling back to 'main'.`
          );
          targetBranch = 'main';
        }
      } catch (error) {
        logger.error(`Error determining default branch:`, error);
        targetBranch = 'main'; // Default to 'main' if we can't determine
      }
    }

    // Construct the URL
    let url = `https://api.github.com/repos/${owner}/${repo}/commits?sha=${encodeURIComponent(
      targetBranch!
    )}&per_page=100`;

    // Add sinceDate parameter if provided
    if (sinceDate) {
      url += `&since=${new Date(sinceDate).toISOString()}`;
    }

    let allCommits: any[] = [];
    let page = startPage;
    let hasMore = true;

    // Fetch commits with pagination
    while (hasMore && allCommits.length < maxCommits) {
      const pageUrl = `${url}&page=${page}`;
      logger.debug(`Fetching commits page ${page} for ${owner}/${repo}`);
      logger.debug(`Constructed pageUrl: ${pageUrl}`);

      const response = await fetch(pageUrl, {
        headers: getHeaders(authToken || undefined),
      });

      if (!response.ok) {
        const errorText = await response.text();
        logger.error(
          `[GitHub API] Error fetching commits: ${response.status} ${response.statusText} - ${errorText}`
        );
        throw new Error(
          `Failed to fetch commits: ${response.status} ${response.statusText}`
        );
      }

      const commits = await response.json();

      if (commits.length === 0) {
        hasMore = false;
      } else {
        allCommits = allCommits.concat(commits);
        page++;
      }

      // GitHub API pagination limit or requested limit reached
      if (commits.length < 100 || allCommits.length >= maxCommits) {
        hasMore = false;
      }
    }

    // Limit to requested maximum
    const limitedCommits = allCommits.slice(0, maxCommits);
    logger.info(
      `[GitHub API] Successfully fetched ${limitedCommits.length} commits for ${owner}/${repo}`
    );

    return limitedCommits;
  } catch (error) {
    logger.error(
      `[GitHub API] Error in getRecentCommits for ${owner}/${repo}:`,
      error
    );
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    throw new Error(`Failed to get recent commits: ${errorMessage}`);
  }
}

/**
 * Fetches the detailed commit data including file changes.
 *
 * @param owner - Repository owner
 * @param repo - Repository name
 * @param sha - Commit SHA
 * @param authToken - Auth token for GitHub API
 * @returns Detailed commit object with files
 */
export async function getCommitDetail(
  owner: string,
  repo: string,
  sha: string,
  authToken: string | null = null
): Promise<CommitDetail> {
  logger.debug(
    `[GitHub API] Fetching commit detail for ${sha} in ${owner}/${repo}`
  );

  try {
    const url = `https://api.github.com/repos/${owner}/${repo}/commits/${sha}`;

    const response = await fetch(url, {
      headers: getHeaders(authToken || undefined),
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error(
        `[GitHub API] Error fetching commit detail: ${response.status} ${response.statusText} - ${errorText}`
      );
      throw new Error(
        `Failed to fetch commit detail: ${response.status} ${response.statusText}`
      );
    }

    const commitDetail = await response.json();
    return commitDetail;
  } catch (error) {
    logger.error(
      `[GitHub API] Error in getCommitDetail for ${sha} in ${owner}/${repo}:`,
      error
    );
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    throw new Error(`Failed to get commit detail: ${errorMessage}`);
  }
}

/**
 * Creates a pseudo PR object from a commit for processing through the existing pipeline.
 *
 * @param commit - Commit object from GitHub API
 * @param commitDetail - Detailed commit object with files
 * @param owner - Repository owner
 * @param repo - Repository name
 * @returns Pseudo PR object matching the structure expected by processMergedPR
 */
export function createPseudoPrFromCommit(
  commit: any,
  commitDetail: CommitDetail,
  owner: string,
  repo: string
): PseudoPR {
  // Extract meaningful commit message (first line is usually the summary)
  const commitMessage = commit.commit.message;
  const firstLineEnd = commitMessage.indexOf('\n');
  const title =
    firstLineEnd > 0
      ? commitMessage.substring(0, firstLineEnd)
      : commitMessage.length > 100
        ? commitMessage.substring(0, 100) + '...'
        : commitMessage;

  // Use rest of message as body if there are multiple lines
  const body =
    firstLineEnd > 0 ? commitMessage.substring(firstLineEnd + 1).trim() : '';

  // Create a timestamp for naming/identification (Unix timestamp)
  const timestamp = new Date(commit.commit.author.date).getTime();

  // Format files similar to PR files
  const files: PRFile[] = commitDetail.files.map(file => ({
    filename: file.filename,
    additions: file.additions,
    deletions: file.deletions,
    patch: file.patch || '',
    status: file.status,
    changes: file.additions + file.deletions,
  }));

  // Create code changes array similar to what's expected for PRs
  const codeChanges = files.map(file => ({
    filename: file.filename,
    patch: file.patch || '',
  }));

  // Build the pseudo PR context
  const prContext = {
    title: `[Commit] ${title}`,
    body: body,
    html_url: commit.html_url,
    number: `commit_${commit.sha.substring(0, 8)}_${timestamp}`, // Use a prefix to identify pseudo PRs
    merged_at: commit.commit.author.date, // Use commit date as "merged" date
    created_at: commit.commit.author.date,
    user: {
      login: commit.commit.author.name || commit.author?.login || 'unknown',
    },
    files: files,
    // Add fields to identify this as a commit-based pseudo PR
    is_pseudo_pr: true,
    commit_sha: commit.sha,
    commit_timestamp: timestamp,
  };

  // Empty comments array (no PR comments for direct commits)
  const comments: any[] = [];

  return {
    prContext,
    codeChanges,
    comments,
  };
}
