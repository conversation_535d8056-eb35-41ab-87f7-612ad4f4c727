import { Request, Response } from 'express';
import {
  getNextPendingPRs,
  processBatch,
  cleanupStuckProcessingItems,
} from '../services/processJobService';
import { logger } from '../utils';

export async function handleProcessJob(req: Request, res: Response) {
  const batchSize = 5;

  try {
    logger.info(
      `[Process Job Controller] Starting process job with batch size: ${batchSize}`
    );

    // First cleanup any stuck processing items
    const cleanupResult = await cleanupStuckProcessingItems();
    if (cleanupResult.cleaned > 0) {
      logger.info(
        `[Process Job Controller] Cleaned up ${cleanupResult.cleaned} stuck processing items`
      );
    }

    // Get next batch of pending PRs
    const pendingPRs = await getNextPendingPRs(batchSize);

    if (pendingPRs.length === 0) {
      logger.info('[Process Job Controller] No pending PRs found to process');
      return res.status(200).json({
        success: true,
        message: 'No pending PRs found to process',
        processed: 0,
        cleanedUp: cleanupResult.cleaned,
      });
    }

    console.log(
      `[Process Job Controller] Found ${pendingPRs.length} pending PRs to process`
    );

    // Process the batch
    const results = await processBatch(pendingPRs);

    const response = {
      success: true,
      message: `Processed ${results.processed} PRs: ${results.successful} successful, ${results.failed} failed, ${results.retried} retried`,
      processed: results.processed,
      successful: results.successful,
      failed: results.failed,
      retried: results.retried,
      cleanedUp: cleanupResult.cleaned,
    };

    logger.info(`[Process Job Controller] Job completed:`, response);
    res.status(200).json(response);
  } catch (error) {
    logger.error('[Process Job Controller] Error in process job:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during PR processing',
      details: error instanceof Error ? error.message : String(error),
    });
  }
}

export async function getProcessJobStatus(req: Request, res: Response) {
  try {
    const pendingPRs = await getNextPendingPRs(1);

    res.status(200).json({
      success: true,
      hasPendingPRs: pendingPRs.length > 0,
      message:
        pendingPRs.length > 0
          ? 'Process job has pending PRs to process'
          : 'No pending PRs for processing',
    });
  } catch (error) {
    logger.error(
      '[Process Job Controller] Error checking process job status:',
      error
    );
    res.status(500).json({
      success: false,
      error: 'Error checking process job status',
      details: error instanceof Error ? error.message : String(error),
    });
  }
}
