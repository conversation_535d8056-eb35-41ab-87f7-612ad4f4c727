import { Request, Response } from 'express';
import {
  getNextPendingEarlyStageItems,
  processBatch,
} from '../services/llmFilterService';
import { logger, getOctokit, checkRateLimit } from '../utils';

export async function handleLlmFilterJob(req: Request, res: Response) {
  const batchSize = 20; // Process 20 items in parallel

  try {
    logger.info(
      `[LLM Filter Controller] Starting LLM filter job with batch size: ${batchSize}`
    );

    // Check GitHub rate limit before starting any work
    try {
      const octokit = await getOctokit();
      const rateLimitStatus = await checkRateLimit(octokit, 100); // Lower threshold for the lightweight job
      if (rateLimitStatus.isLimited) {
        const message = `GitHub rate limit is low (${rateLimitStatus.remaining} remaining). Pausing job until ${rateLimitStatus.resetTime.toLocaleTimeString()}.`;
        logger.warn(`[LLM Filter Controller] ${message}`);
        return res.status(200).json({
          action: 'no_action',
          message: message,
          details: { reason: 'RATE_LIMIT_PAUSE' },
        });
      }
    } catch (error) {
      logger.warn(
        `[LLM Filter Controller] Could not check rate limit, proceeding with caution:`,
        error
      );
    }

    // Get next batch of items to process
    const items = await getNextPendingEarlyStageItems(batchSize);

    if (items.length === 0) {
      logger.info(
        '[LLM Filter Controller] No pending_early_stage items found to process'
      );
      return res.status(200).json({
        success: true,
        message: 'No pending_early_stage items found to process',
        processed: 0,
      });
    }

    logger.info(
      `[LLM Filter Controller] Found ${items.length} pending_early_stage items to process`
    );

    // Process the batch
    const results = await processBatch(items);

    const response = {
      success: true,
      message: `Processed ${results.processed} items: ${results.pending} → pending, ${results.skipped} → skipped, ${results.limitReached} → limit reached`,
      details: {
        batch_size: results.processed,
        successful_updates: results.successful,
        failed_updates: results.failed,
        items_marked_pending: results.pending,
        items_marked_skipped: results.skipped,
        items_marked_limit_reached: results.limitReached,
      },
    };

    logger.info(`[LLM Filter Controller] Job completed:`, response);
    res.status(200).json(response);
  } catch (error) {
    logger.error('[LLM Filter Controller] Error in LLM filter job:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during LLM filter processing',
      details: error instanceof Error ? error.message : String(error),
    });
  }
}

export async function getLlmFilterStatus(req: Request, res: Response) {
  try {
    const items = await getNextPendingEarlyStageItems(1);

    res.status(200).json({
      success: true,
      hasPendingItems: items.length > 0,
      message:
        items.length > 0
          ? 'LLM filter has pending items to process'
          : 'No pending items for LLM filter',
    });
  } catch (error) {
    logger.error(
      '[LLM Filter Controller] Error checking LLM filter status:',
      error
    );
    res.status(500).json({
      success: false,
      error: 'Error checking LLM filter status',
      details: error instanceof Error ? error.message : String(error),
    });
  }
}
