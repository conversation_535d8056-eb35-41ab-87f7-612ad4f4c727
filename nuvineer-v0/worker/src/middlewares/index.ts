import { Request, Response, NextFunction } from 'express';
import { logger, validateCronSecret } from '../utils';

/**
 * Middleware to validate cron job secret authentication
 */
export const cronAuthMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  if (!validateCronSecret(req)) {
    logger.warn(
      '[Auth Middleware] Unauthorized attempt: Incorrect or missing secret.'
    );
    return res.status(401).json({
      action: 'error',
      message: 'Unauthorized: Incorrect or missing secret.',
    });
  }
  next();
};

/**
 * Middleware for logging requests
 */
export const loggerMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const timestamp = new Date().toISOString();
  logger.info(`[${timestamp}] ${req.method} ${req.path} - ${req.ip}`);
  next();
};

/**
 * Error handling middleware
 */
export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  logger.error(`[Error] ${req.method} ${req.path}:`, err);

  res.status(500).json({
    action: 'error',
    message: 'Internal server error',
    details: process.env.NODE_ENV === 'development' ? err.message : undefined,
  });
};
