# Nuvineer Express Server

This Express server provides endpoints for repository analysis jobs that were previously implemented as Next.js API routes.

## Architecture

The server follows a clean, organized folder structure with proper separation of concerns:

```
src/
├── config/           # Configuration management
├── controllers/      # Request/response handlers
├── routes/          # Route definitions
├── services/        # Business logic
├── middlewares/     # Express middlewares
├── utils/           # Shared utilities
├── types/           # TypeScript type definitions
├── app.ts           # Express app setup
└── server.ts        # Server entry point
```

## Endpoints

### Repository Loader Job

- `POST /api/analysis/repository-loader-job` - Process repository loading job
- `GET /api/analysis/repository-loader-job` - Check repository loader status

### LLM Filter Job

- `POST /api/analysis/llm-filter-job` - Process LLM significance filtering
- `GET /api/analysis/llm-filter-job` - Check LLM filter status

### Process Job

- `POST /api/analysis/process-job` - Process pending PRs/commits for analysis
- `GET /api/analysis/process-job` - Check process job status

## Environment Variables

The server uses the same environment variables as the main Next.js application:

- `NODE_ENV` - Environment (development/production)
- `PORT` - Server port (default: 3001)
- `CRON_SECRET` - Secret for authenticating cron job requests
- `SUPABASE_URL` - Supabase URL
- `SUPABASE_SERVICE_ROLE_KEY` - Supabase service role key
- GitHub App credentials and other variables from the main app

## Development

```bash
# Install dependencies
pnpm install

# Run in development mode with hot reload
pnpm dev

# Build for production
pnpm build

# Start production server
pnpm start
```

## Security

- All endpoints require CRON_SECRET authentication via header or query parameter
- CORS configured for production origins
- Helmet security headers applied
- Request rate limiting and body parsing limits
- Compression enabled for responses

## Integration

This server interfaces with:

- **Supabase**: Database operations for job tracking and status updates
- **GitHub API**: Repository data fetching via Octokit
- **Main Codebase**: Imports services and utilities from the Next.js application
- **Vector Database**: Analysis and embedding operations

## Deployment

The server is designed to be deployed separately from the main Next.js application and can be scaled independently to handle analysis workloads.
