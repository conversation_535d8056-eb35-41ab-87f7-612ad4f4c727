-- Migration: Update vector_similarity_search function to include namespace parameter
-- This eliminates the need for RLS context setting and provides direct namespace filtering

-- Drop the existing function first
DROP FUNCTION IF EXISTS vector_similarity_search(vector, float, int, jsonb);

-- Create updated vector similarity search function with namespace parameter
CREATE OR REPLACE FUNCTION vector_similarity_search(
  query_vector vector,
  target_namespace text,
  similarity_threshold float DEFAULT 0,
  max_results int DEFAULT 10,
  table_filter jsonb DEFAULT '{}'::jsonb
)
RETURNS TABLE(
  id text,
  metadata jsonb, 
  distance float,
  embedding vector
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    dv.id,
    dv.metadata,
    dv.embedding <=> query_vector as distance,
    dv.embedding
  FROM decision_vectors dv
  WHERE 
    dv.namespace = target_namespace
    AND (table_filter = '{}'::jsonb OR dv.metadata @> table_filter)
    AND (similarity_threshold = 0 OR (dv.embedding <=> query_vector) >= similarity_threshold)
  ORDER BY dv.embedding <=> query_vector
  LIMIT max_results;
END;
$$;

-- <PERSON> execute permission on the updated function
GRANT EXECUTE ON FUNCTION vector_similarity_search TO authenticated;

-- Update function comment
COMMENT ON FUNCTION vector_similarity_search IS 'Performs vector similarity search with namespace filtering, eliminating need for RLS context';

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'Updated vector_similarity_search function to include namespace parameter';
    RAISE NOTICE 'Function now filters by namespace directly without requiring RLS context';
END $$;
