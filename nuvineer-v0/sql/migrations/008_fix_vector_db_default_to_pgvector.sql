-- Migration: Fix vector_db default to pgvector
-- Change default from pinecone to pgvector for new repositories

-- Update the default value for the vector_db column
ALTER TABLE public.repository_settings 
ALTER COLUMN vector_db SET DEFAULT 'pgvector';

-- Update existing rows that are set to pinecone to pgvector
UPDATE public.repository_settings 
SET vector_db = 'pgvector' 
WHERE vector_db = 'pinecone';

-- Add comment for documentation
COMMENT ON COLUMN public.repository_settings.vector_db IS 'Vector database used for this repository: pinecone or pgvector (default: pgvector)'; 