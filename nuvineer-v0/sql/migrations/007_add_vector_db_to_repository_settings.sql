-- Migration: Add vector_db field to repository_settings table
-- This field will track which vector database is used for each repository
-- Default to "pinecone" for existing repositories to maintain backward compatibility

ALTER TABLE public.repository_settings 
ADD COLUMN vector_db text NOT NULL DEFAULT 'pinecone' 
CHECK (vector_db IN ('pinecone', 'pgvector'));

-- Create index for faster lookups by vector_db
CREATE INDEX idx_repository_settings_vector_db ON public.repository_settings(vector_db);

-- Add comment for documentation
COMMENT ON COLUMN public.repository_settings.vector_db IS 'Vector database used for this repository: pinecone or pgvector';

-- Update existing rows to explicitly set pinecone (even though default handles this)
UPDATE public.repository_settings SET vector_db = 'pinecone' WHERE vector_db IS NULL; 