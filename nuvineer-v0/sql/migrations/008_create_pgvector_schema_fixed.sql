-- Migration: Create pgvector schema for vector database operations (FIXED)
-- This creates the necessary tables and indexes for pgvector support

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS vector;

-- Create decision vectors table with composite primary key for namespace isolation
CREATE TABLE IF NOT EXISTS decision_vectors (
    id TEXT NOT NULL,
    namespace TEXT NOT NULL,
    embedding vector(1536), -- OpenAI text-embedding-3-small dimension
    metadata JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Composite primary key ensures (id, namespace) uniqueness
    PRIMARY KEY (id, namespace)
);

-- Create basic indexes for performance
CREATE INDEX IF NOT EXISTS idx_decision_vectors_namespace ON decision_vectors(namespace);
CREATE INDEX IF NOT EXISTS idx_decision_vectors_metadata_ns ON decision_vectors(namespace) INCLUDE (metadata);
CREATE INDEX IF NOT EXISTS idx_decision_vectors_created_at ON decision_vectors(created_at);
CREATE INDEX IF NOT EXISTS idx_decision_vectors_updated_at ON decision_vectors(updated_at);

-- Create IVFFlat index for vector similarity search (general purpose)
CREATE INDEX IF NOT EXISTS idx_decision_vectors_embedding_ivfflat ON decision_vectors 
USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Enable Row Level Security for namespace isolation
ALTER TABLE decision_vectors ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for namespace isolation
-- This ensures that queries can only access vectors in the correct namespace
DROP POLICY IF EXISTS namespace_isolation ON decision_vectors;
CREATE POLICY namespace_isolation ON decision_vectors
    FOR ALL 
    TO authenticated
    USING (namespace = current_setting('app.current_namespace', true));

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON decision_vectors TO authenticated;

-- Create function to set namespace context
CREATE OR REPLACE FUNCTION set_vector_namespace(target_namespace TEXT)
RETURNS void AS $$
BEGIN
    PERFORM set_config('app.current_namespace', target_namespace, true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the namespace function
GRANT EXECUTE ON FUNCTION set_vector_namespace TO authenticated;

-- Create concept vectors table for domain concept normalization
CREATE TABLE IF NOT EXISTS concept_vectors (
    id TEXT NOT NULL,
    namespace TEXT NOT NULL,
    embedding vector(1536),
    metadata JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    PRIMARY KEY (id, namespace)
);

-- Create indexes for concept vectors
CREATE INDEX IF NOT EXISTS idx_concept_vectors_namespace ON concept_vectors(namespace);
CREATE INDEX IF NOT EXISTS idx_concept_vectors_embedding_ivfflat ON concept_vectors 
USING ivfflat (embedding vector_cosine_ops) WITH (lists = 50);

-- Enable RLS for concept vectors
ALTER TABLE concept_vectors ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for concept vectors
DROP POLICY IF EXISTS concept_namespace_isolation ON concept_vectors;
CREATE POLICY concept_namespace_isolation ON concept_vectors
    FOR ALL 
    TO authenticated
    USING (namespace = current_setting('app.current_namespace', true));

-- Grant permissions for concept vectors
GRANT SELECT, INSERT, UPDATE, DELETE ON concept_vectors TO authenticated;

-- Create journey vectors table for user journey storage
CREATE TABLE IF NOT EXISTS journey_vectors (
    id TEXT NOT NULL,
    namespace TEXT NOT NULL,
    embedding vector(1536),
    metadata JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    PRIMARY KEY (id, namespace)
);

-- Create indexes for journey vectors
CREATE INDEX IF NOT EXISTS idx_journey_vectors_namespace ON journey_vectors(namespace);
CREATE INDEX IF NOT EXISTS idx_journey_vectors_embedding_ivfflat ON journey_vectors 
USING ivfflat (embedding vector_cosine_ops) WITH (lists = 50);

-- Enable RLS for journey vectors
ALTER TABLE journey_vectors ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for journey vectors
DROP POLICY IF EXISTS journey_namespace_isolation ON journey_vectors;
CREATE POLICY journey_namespace_isolation ON journey_vectors
    FOR ALL 
    TO authenticated
    USING (namespace = current_setting('app.current_namespace', true));

-- Grant permissions for journey vectors
GRANT SELECT, INSERT, UPDATE, DELETE ON journey_vectors TO authenticated;

-- Create helper functions for vector operations
CREATE OR REPLACE FUNCTION cosine_similarity(a vector, b vector)
RETURNS float AS $$
BEGIN
    RETURN 1 - (a <=> b);
END;
$$ LANGUAGE plpgsql IMMUTABLE STRICT;

-- Create function to get vector statistics for a namespace
CREATE OR REPLACE FUNCTION get_namespace_vector_stats(target_namespace TEXT)
RETURNS TABLE(
    total_vectors bigint,
    active_vectors bigint,
    superseded_vectors bigint,
    avg_embedding_magnitude float,
    created_today bigint
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_vectors,
        COUNT(*) FILTER (WHERE (metadata->>'is_superseded')::boolean = false) as active_vectors,
        COUNT(*) FILTER (WHERE (metadata->>'is_superseded')::boolean = true) as superseded_vectors,
        AVG(vector_norm(embedding)) as avg_embedding_magnitude,
        COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE) as created_today
    FROM decision_vectors 
    WHERE namespace = target_namespace;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on stats function
GRANT EXECUTE ON FUNCTION get_namespace_vector_stats TO authenticated;

-- Create function to cleanup old superseded vectors (optional maintenance)
CREATE OR REPLACE FUNCTION cleanup_superseded_vectors(
    target_namespace TEXT,
    older_than_days INTEGER DEFAULT 30
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM decision_vectors 
    WHERE namespace = target_namespace
      AND (metadata->>'is_superseded')::boolean = true
      AND updated_at < (CURRENT_DATE - INTERVAL '%s days', older_than_days);
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on cleanup function
GRANT EXECUTE ON FUNCTION cleanup_superseded_vectors TO authenticated;

-- Create vector similarity search function to handle complex queries more reliably
CREATE OR REPLACE FUNCTION vector_similarity_search(
  query_vector vector,
  similarity_threshold float DEFAULT 0,
  max_results int DEFAULT 10,
  table_filter jsonb DEFAULT '{}'::jsonb
)
RETURNS TABLE(
  id text,
  metadata jsonb, 
  distance float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    dv.id,
    dv.metadata,
    dv.embedding <=> query_vector as distance
  FROM decision_vectors dv
  WHERE 
    (table_filter = '{}'::jsonb OR dv.metadata @> table_filter)
    AND (similarity_threshold = 0 OR (dv.embedding <=> query_vector) >= similarity_threshold)
  ORDER BY dv.embedding <=> query_vector
  LIMIT max_results;
END;
$$;

-- Grant execute permission on vector similarity search function
GRANT EXECUTE ON FUNCTION vector_similarity_search TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE decision_vectors IS 'Stores architectural decision vectors with namespace isolation';
COMMENT ON TABLE concept_vectors IS 'Stores domain concept vectors for normalization';
COMMENT ON TABLE journey_vectors IS 'Stores user journey vectors for design doc workflows';

COMMENT ON COLUMN decision_vectors.id IS 'Unique identifier for the decision vector';
COMMENT ON COLUMN decision_vectors.namespace IS 'Repository namespace in format: {installationId}-{owner}--{repo}';
COMMENT ON COLUMN decision_vectors.embedding IS 'OpenAI text-embedding-3-small vector (1536 dimensions)';
COMMENT ON COLUMN decision_vectors.metadata IS 'Decision metadata including title, description, PR info, etc.';

COMMENT ON FUNCTION set_vector_namespace IS 'Sets the current namespace for RLS policy enforcement';
COMMENT ON FUNCTION get_namespace_vector_stats IS 'Returns vector statistics for a specific namespace';
COMMENT ON FUNCTION cleanup_superseded_vectors IS 'Removes old superseded vectors for maintenance';
COMMENT ON FUNCTION vector_similarity_search IS 'Performs vector similarity search with filtering and thresholding';

-- Create simple B-tree indexes for common metadata queries (avoiding GIN issues)
CREATE INDEX IF NOT EXISTS idx_decision_vectors_metadata_repo_slug 
ON decision_vectors ((metadata->>'repository_slug'));

CREATE INDEX IF NOT EXISTS idx_decision_vectors_metadata_is_superseded 
ON decision_vectors ((metadata->>'is_superseded'));

CREATE INDEX IF NOT EXISTS idx_decision_vectors_metadata_pr_number 
ON decision_vectors ((metadata->>'pr_number'));

-- Create JSONB GIN indexes for array operations (these should work)
CREATE INDEX IF NOT EXISTS idx_decision_vectors_metadata_jsonb 
ON decision_vectors USING GIN (metadata);

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'pgvector schema migration completed successfully (FIXED VERSION)';
    RAISE NOTICE 'Tables created: decision_vectors, concept_vectors, journey_vectors';
    RAISE NOTICE 'Row Level Security enabled with namespace isolation';
    RAISE NOTICE 'Vector indexes created for performance optimization';
    RAISE NOTICE 'Simple B-tree indexes used to avoid GIN operator class issues';
END $$; 