-- Migration: Create repository_settings table
-- This table stores configuration settings for each repository including analysis limits

CREATE TABLE public.repository_settings (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  repository_slug text NOT NULL,
  max_commits_deep_analysis int NOT NULL DEFAULT 500,
  tech_debt_analysis_enabled boolean NOT NULL DEFAULT false,
  created_at timestamp with time zone NULL DEFAULT now(),
  updated_at timestamp with time zone NULL DEFAULT now(),
  CONSTRAINT repository_settings_pkey PRIMARY KEY (id),
  CONSTRAINT repository_settings_repository_slug_key UNIQUE (repository_slug)
) TABLESPACE pg_default;

-- Create index for faster lookups by repository_slug
CREATE INDEX idx_repository_settings_repository_slug ON public.repository_settings(repository_slug);

-- Add RLS policies if needed (uncomment if using Row Level Security)
-- ALTER TABLE public.repository_settings ENABLE ROW LEVEL SECURITY;

-- Grant necessary permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON public.repository_settings TO authenticated;
-- GRANT USAGE ON SEQUENCE repository_settings_id_seq TO authenticated; 